{"version": 3, "sources": ["angular:styles/component:css;807c68d7596a469b9f7583c62f636b97e4375dcb5b56425d092dcfbea1142c31;D:/learning/angular/standalone/src/app/components/about/about.component.ts"], "sourcesContent": ["\n    .about {\n      max-width: 1000px;\n      margin: 0 auto;\n    }\n\n    .tech-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 20px;\n      margin: 20px 0;\n    }\n\n    .tech-item {\n      padding: 20px;\n      background: #f8f9fa;\n      border-radius: 8px;\n      text-align: center;\n      border: 1px solid #e9ecef;\n    }\n\n    .tech-item h4 {\n      margin: 0 0 10px 0;\n      color: #1976d2;\n    }\n\n    .tech-item p {\n      margin: 0;\n      color: #666;\n      font-size: 14px;\n    }\n\n    .features-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n      gap: 20px;\n      margin: 20px 0;\n    }\n\n    .feature-card {\n      padding: 20px;\n      background: white;\n      border: 1px solid #e9ecef;\n      border-radius: 8px;\n      box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n      transition: transform 0.3s ease;\n    }\n\n    .feature-card:hover {\n      transform: translateY(-2px);\n      box-shadow: 0 4px 8px rgba(0,0,0,0.15);\n    }\n\n    .feature-card h4 {\n      margin: 0 0 10px 0;\n      color: #333;\n      font-size: 16px;\n    }\n\n    .feature-card p {\n      margin: 0;\n      color: #666;\n      font-size: 14px;\n      line-height: 1.5;\n    }\n\n    .architecture-info {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: 30px;\n      margin: 20px 0;\n    }\n\n    .arch-item {\n      background: #f8f9fa;\n      padding: 20px;\n      border-radius: 8px;\n    }\n\n    .arch-item h4 {\n      margin: 0 0 15px 0;\n      color: #1976d2;\n    }\n\n    .arch-item ul {\n      margin: 0;\n      padding-left: 20px;\n    }\n\n    .arch-item li {\n      margin: 8px 0;\n      color: #555;\n    }\n\n    .version-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 15px;\n      margin: 20px 0;\n    }\n\n    .version-item {\n      padding: 15px;\n      background: #e9ecef;\n      border-radius: 8px;\n      text-align: center;\n      font-size: 14px;\n    }\n\n    h2 {\n      color: #1976d2;\n      margin-bottom: 20px;\n    }\n\n    h3 {\n      color: #333;\n      margin: 30px 0 15px 0;\n      border-bottom: 2px solid #1976d2;\n      padding-bottom: 5px;\n    }\n\n    .features-section,\n    .architecture-section,\n    .version-info {\n      margin: 40px 0;\n    }\n\n    @media (max-width: 768px) {\n      .architecture-info {\n        grid-template-columns: 1fr;\n      }\n      \n      .features-grid {\n        grid-template-columns: 1fr;\n      }\n      \n      .tech-grid {\n        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n      }\n    }\n  "], "mappings": ";AACI,CAAC;AACC,aAAW;AACX,UAAQ,EAAE;AACZ;AAEA,CAAC;AACC,WAAS;AACT,yBAAuB,OAAO,QAAQ,EAAE,OAAO,KAAK,EAAE;AACtD,OAAK;AACL,UAAQ,KAAK;AACf;AAEA,CAAC;AACC,WAAS;AACT,cAAY;AACZ,iBAAe;AACf,cAAY;AACZ,UAAQ,IAAI,MAAM;AACpB;AAEA,CARC,UAQU;AACT,UAAQ,EAAE,EAAE,KAAK;AACjB,SAAO;AACT;AAEA,CAbC,UAaU;AACT,UAAQ;AACR,SAAO;AACP,aAAW;AACb;AAEA,CAAC;AACC,WAAS;AACT,yBAAuB,OAAO,QAAQ,EAAE,OAAO,KAAK,EAAE;AACtD,OAAK;AACL,UAAQ,KAAK;AACf;AAEA,CAAC;AACC,WAAS;AACT,cAAY;AACZ,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;AACjC,cAAY,UAAU,KAAK;AAC7B;AAEA,CATC,YASY;AACX,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;AACnC;AAEA,CAdC,aAca;AACZ,UAAQ,EAAE,EAAE,KAAK;AACjB,SAAO;AACP,aAAW;AACb;AAEA,CApBC,aAoBa;AACZ,UAAQ;AACR,SAAO;AACP,aAAW;AACX,eAAa;AACf;AAEA,CAAC;AACC,WAAS;AACT,yBAAuB,IAAI;AAC3B,OAAK;AACL,UAAQ,KAAK;AACf;AAEA,CAAC;AACC,cAAY;AACZ,WAAS;AACT,iBAAe;AACjB;AAEA,CANC,UAMU;AACT,UAAQ,EAAE,EAAE,KAAK;AACjB,SAAO;AACT;AAEA,CAXC,UAWU;AACT,UAAQ;AACR,gBAAc;AAChB;AAEA,CAhBC,UAgBU;AACT,UAAQ,IAAI;AACZ,SAAO;AACT;AAEA,CAAC;AACC,WAAS;AACT,yBAAuB,OAAO,QAAQ,EAAE,OAAO,KAAK,EAAE;AACtD,OAAK;AACL,UAAQ,KAAK;AACf;AAEA,CAAC;AACC,WAAS;AACT,cAAY;AACZ,iBAAe;AACf,cAAY;AACZ,aAAW;AACb;AAEA;AACE,SAAO;AACP,iBAAe;AACjB;AAEA;AACE,SAAO;AACP,UAAQ,KAAK,EAAE,KAAK;AACpB,iBAAe,IAAI,MAAM;AACzB,kBAAgB;AAClB;AAEA,CAAC;AACD,CAAC;AACD,CAAC;AACC,UAAQ,KAAK;AACf;AAEA,OAAO,CAAC,SAAS,EAAE;AACjB,GA9DD;AA+DG,2BAAuB;AACzB;AAEA,GApGD;AAqGG,2BAAuB;AACzB;AAEA,GAlID;AAmIG,2BAAuB,OAAO,QAAQ,EAAE,OAAO,KAAK,EAAE;AACxD;AACF;", "names": []}