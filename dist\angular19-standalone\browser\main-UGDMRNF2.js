import{a as s,b as f,c as C,d as v,e as g}from"./chunk-5YXZ7YVB.js";import{$a as d,Ia as m,La as o,Ma as t,Na as l,Ua as n,Va as c,nb as u,qa as p,ya as a}from"./chunk-XF3IHJ6T.js";var k=()=>({exact:!0}),i=class e{constructor(){this.title="Angular 19 Standalone Components"}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275cmp=a({type:e,selectors:[["app-root"]],decls:25,vars:3,consts:[[1,"app"],["routerLink","/","routerLinkActive","active",3,"routerLinkActiveOptions"],["routerLink","/counter","routerLinkActive","active"],["routerLink","/users","routerLinkActive","active"],["routerLink","/about","routerLinkActive","active"],[1,"container"]],template:function(r,x){r&1&&(o(0,"div",0)(1,"header")(2,"nav")(3,"ul")(4,"li")(5,"a",1),n(6,"Home"),t()(),o(7,"li")(8,"a",2),n(9,"Counter"),t()(),o(10,"li")(11,"a",3),n(12,"Users"),t()(),o(13,"li")(14,"a",4),n(15,"About"),t()()()()(),o(16,"main",5)(17,"h1"),n(18),t(),o(19,"p"),n(20,"Welcome to Angular 19 with Standalone Components!"),t(),l(21,"router-outlet"),t(),o(22,"footer",5)(23,"p"),n(24,"\xA9 2024 Angular 19 Standalone Example"),t()()()),r&2&&(p(5),m("routerLinkActiveOptions",d(2,k)),p(13),c(x.title))},dependencies:[f,C,v,u],styles:[".app[_ngcontent-%COMP%]{min-height:100vh;display:flex;flex-direction:column}header[_ngcontent-%COMP%]{background:#1976d2;color:#fff;padding:1rem 0}main[_ngcontent-%COMP%]{flex:1;padding:2rem 0}footer[_ngcontent-%COMP%]{background:#f5f5f5;padding:1rem 0;text-align:center;border-top:1px solid #ddd}h1[_ngcontent-%COMP%]{color:#1976d2;margin-bottom:1rem}"]})}};var h=[{path:"",loadComponent:()=>import("./chunk-WQMCK4FL.js").then(e=>e.HomeComponent)},{path:"counter",loadComponent:()=>import("./chunk-MYUIVQTG.js").then(e=>e.CounterComponent)},{path:"users",loadComponent:()=>import("./chunk-BGUVO2K5.js").then(e=>e.UsersComponent)},{path:"about",loadComponent:()=>import("./chunk-GLKUBOLV.js").then(e=>e.AboutComponent)},{path:"**",redirectTo:""}];s(i,{providers:[g(h)]}).catch(e=>console.error(e));
