import { Component, Output, EventEmitter, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

export interface Task {
  id: number;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  completed: boolean;
  createdAt: Date;
}

@Component({
  selector: 'app-task-form',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="task-form">
      <h3>{{ editMode() ? 'Edit Task' : 'Add New Task' }}</h3>
      
      <form (ngSubmit)="onSubmit()" #taskForm="ngForm">
        <div class="form-group">
          <label for="title">Task Title *</label>
          <input 
            type="text" 
            id="title"
            name="title"
            [(ngModel)]="taskData.title"
            required
            #titleInput="ngModel"
            class="form-control"
            placeholder="Enter task title..."
          >
          <div *ngIf="titleInput.invalid && titleInput.touched" class="error">
            Title is required
          </div>
        </div>

        <div class="form-group">
          <label for="description">Description</label>
          <textarea 
            id="description"
            name="description"
            [(ngModel)]="taskData.description"
            class="form-control"
            rows="3"
            placeholder="Enter task description..."
          ></textarea>
        </div>

        <div class="form-group">
          <label for="priority">Priority</label>
          <select 
            id="priority"
            name="priority"
            [(ngModel)]="taskData.priority"
            class="form-control"
          >
            <option value="low">Low</option>
            <option value="medium">Medium</option>
            <option value="high">High</option>
          </select>
        </div>

        <div class="form-actions">
          <button 
            type="submit" 
            class="btn btn-primary"
            [disabled]="taskForm.invalid"
          >
            {{ editMode() ? 'Update Task' : 'Add Task' }}
          </button>
          
          <button 
            type="button" 
            class="btn btn-secondary"
            (click)="onCancel()"
          >
            Cancel
          </button>
          
          <button 
            type="button" 
            class="btn btn-danger"
            (click)="onReset()"
          >
            Reset Form
          </button>
        </div>
      </form>

      <div class="form-info">
        <h4>Form Status:</h4>
        <p>Valid: {{ taskForm.valid }}</p>
        <p>Touched: {{ taskForm.touched }}</p>
        <p>Edit Mode: {{ editMode() }}</p>
      </div>
    </div>
  `,
  styles: [`
    .task-form {
      background: white;
      padding: 20px;
      border-radius: 8px;
      border: 1px solid #ddd;
      max-width: 500px;
      margin: 0 auto;
    }

    .task-form h3 {
      margin: 0 0 20px 0;
      color: #333;
      text-align: center;
    }

    .form-group {
      margin-bottom: 20px;
    }

    .form-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
      color: #555;
    }

    .form-control {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      box-sizing: border-box;
    }

    .form-control:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
    }

    .error {
      color: #dc3545;
      font-size: 12px;
      margin-top: 5px;
    }

    .form-actions {
      display: flex;
      gap: 10px;
      justify-content: center;
      margin-top: 20px;
    }

    .btn {
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.3s;
    }

    .btn-primary {
      background: #007bff;
      color: white;
    }

    .btn-primary:hover:not(:disabled) {
      background: #0056b3;
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background: #545b62;
    }

    .btn-danger {
      background: #dc3545;
      color: white;
    }

    .btn-danger:hover {
      background: #c82333;
    }

    .btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .form-info {
      margin-top: 20px;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 4px;
      border-left: 4px solid #17a2b8;
    }

    .form-info h4 {
      margin: 0 0 10px 0;
      color: #17a2b8;
    }

    .form-info p {
      margin: 5px 0;
      font-size: 14px;
    }

    @media (max-width: 768px) {
      .form-actions {
        flex-direction: column;
      }
      
      .btn {
        width: 100%;
      }
    }
  `]
})
export class TaskFormComponent {
  // @Output decorators - emit events to parent component
  @Output() taskSubmitted = new EventEmitter<Task>();
  @Output() formCancelled = new EventEmitter<void>();
  @Output() formReset = new EventEmitter<void>();

  // Signal to track edit mode
  editMode = signal(false);

  // Form data
  taskData = {
    title: '',
    description: '',
    priority: 'medium' as 'low' | 'medium' | 'high'
  };

  onSubmit() {
    if (this.taskData.title.trim()) {
      const newTask: Task = {
        id: Date.now(), // Simple ID generation
        title: this.taskData.title.trim(),
        description: this.taskData.description.trim(),
        priority: this.taskData.priority,
        completed: false,
        createdAt: new Date()
      };

      // Emit the task to parent component
      this.taskSubmitted.emit(newTask);
      
      // Reset form after submission
      this.resetForm();
    }
  }

  onCancel() {
    // Emit cancel event to parent
    this.formCancelled.emit();
    this.resetForm();
  }

  onReset() {
    // Emit reset event to parent
    this.formReset.emit();
    this.resetForm();
  }

  private resetForm() {
    this.taskData = {
      title: '',
      description: '',
      priority: 'medium'
    };
    this.editMode.set(false);
  }

  // Method to populate form for editing (can be called by parent)
  editTask(task: Task) {
    this.taskData = {
      title: task.title,
      description: task.description,
      priority: task.priority
    };
    this.editMode.set(true);
  }
}
