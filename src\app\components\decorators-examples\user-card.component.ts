import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface User {
  id: number;
  name: string;
  email: string;
  avatar?: string;
  role: string;
  isActive: boolean;
}

@Component({
  selector: 'app-user-card',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="user-card" [class.inactive]="!user.isActive">
      <div class="user-avatar">
        <img 
          [src]="user.avatar || defaultAvatar" 
          [alt]="user.name + ' avatar'"
          class="avatar-img"
        >
      </div>
      
      <div class="user-info">
        <h3>{{ user.name }}</h3>
        <p class="email">{{ user.email }}</p>
        <span class="role-badge" [class]="'role-' + user.role.toLowerCase()">
          {{ user.role }}
        </span>
        <span class="status-badge" [class.active]="user.isActive">
          {{ user.isActive ? 'Active' : 'Inactive' }}
        </span>
      </div>

      <div class="user-actions">
        <button class="btn btn-sm" [disabled]="!showActions">
          Edit
        </button>
        <button class="btn btn-sm btn-secondary" [disabled]="!showActions">
          Delete
        </button>
      </div>
    </div>
  `,
  styles: [`
    .user-card {
      display: flex;
      align-items: center;
      gap: 15px;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 8px;
      background: white;
      margin: 10px 0;
      transition: all 0.3s ease;
    }

    .user-card:hover {
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      transform: translateY(-2px);
    }

    .user-card.inactive {
      opacity: 0.6;
      background: #f8f9fa;
    }

    .user-avatar {
      flex-shrink: 0;
    }

    .avatar-img {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid #e9ecef;
    }

    .user-info {
      flex: 1;
    }

    .user-info h3 {
      margin: 0 0 5px 0;
      color: #333;
      font-size: 18px;
    }

    .email {
      margin: 0 0 10px 0;
      color: #666;
      font-size: 14px;
    }

    .role-badge, .status-badge {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
      margin-right: 8px;
    }

    .role-admin { background: #dc3545; color: white; }
    .role-manager { background: #ffc107; color: black; }
    .role-user { background: #28a745; color: white; }

    .status-badge {
      background: #6c757d;
      color: white;
    }

    .status-badge.active {
      background: #28a745;
    }

    .user-actions {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .btn-sm {
      padding: 6px 12px;
      font-size: 12px;
      min-width: 60px;
    }

    .btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    @media (max-width: 768px) {
      .user-card {
        flex-direction: column;
        text-align: center;
      }
      
      .user-actions {
        flex-direction: row;
        justify-content: center;
      }
    }
  `]
})
export class UserCardComponent {
  // @Input decorator - receives data from parent component
  @Input({ required: true }) user!: User;
  @Input() showActions: boolean = true;
  @Input() defaultAvatar: string = 'https://via.placeholder.com/60x60/007bff/ffffff?text=U';
}
