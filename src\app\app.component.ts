import { Component } from '@angular/core';
import { RouterOutlet, RouterLink, RouterLinkActive } from '@angular/router';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, RouterLink, RouterLinkActive, CommonModule],
  template: `
    <div class="app">
      <header>
        <nav>
          <ul>
            <li><a routerLink="/" routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}">Home</a></li>
            <li><a routerLink="/counter" routerLinkActive="active">Counter</a></li>
            <li><a routerLink="/users" routerLinkActive="active">Users</a></li>
            <li><a routerLink="/decorators" routerLinkActive="active">Decorators</a></li>
            <li><a routerLink="/about" routerLinkActive="active">About</a></li>
          </ul>
        </nav>
      </header>

      <main class="container">
        <h1>{{ title }}</h1>
        <p>Welcome to Angular 19 with Standalone Components!</p>
        
        <router-outlet></router-outlet>
      </main>

      <footer class="container">
        <p>&copy; 2024 Angular 19 Standalone Example</p>
      </footer>
    </div>
  `,
  styles: [`
    .app {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }

    header {
      background: #1976d2;
      color: white;
      padding: 1rem 0;
    }

    main {
      flex: 1;
      padding: 2rem 0;
    }

    footer {
      background: #f5f5f5;
      padding: 1rem 0;
      text-align: center;
      border-top: 1px solid #ddd;
    }

    h1 {
      color: #1976d2;
      margin-bottom: 1rem;
    }
  `]
})
export class AppComponent {
  title = 'Angular 19 Standalone Components';
}
