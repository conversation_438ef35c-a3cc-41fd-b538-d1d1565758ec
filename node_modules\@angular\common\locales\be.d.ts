/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    AUD: string[];
    BBD: (string | undefined)[];
    BMD: (string | undefined)[];
    BRL: (string | undefined)[];
    BSD: (string | undefined)[];
    BYN: string[];
    BZD: (string | undefined)[];
    CAD: (string | undefined)[];
    CUC: (string | undefined)[];
    CUP: (string | undefined)[];
    DOP: (string | undefined)[];
    FJD: (string | undefined)[];
    FKP: (string | undefined)[];
    GYD: (string | undefined)[];
    ISK: (string | undefined)[];
    JMD: (string | undefined)[];
    KYD: (string | undefined)[];
    LRD: (string | undefined)[];
    MXN: string[];
    NAD: (string | undefined)[];
    NZD: (string | undefined)[];
    PHP: (string | undefined)[];
    RUB: string[];
    SBD: (string | undefined)[];
    SGD: (string | undefined)[];
    TTD: (string | undefined)[];
    UYU: (string | undefined)[];
    XCD: string[];
} | undefined)[];
export default _default;
