import { Component, Input, Output, EventEmitter, ViewChild, ElementRef, AfterViewInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

// Simple Child Component demonstrating @Input
@Component({
  selector: 'app-child-demo',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="child-demo">
      <h4>Child Component</h4>
      <p>Message from parent: <strong>{{ message }}</strong></p>
      <p>Count from parent: <strong>{{ count }}</strong></p>
      <p>Show details: <strong>{{ showDetails ? 'Yes' : 'No' }}</strong></p>
      
      <div *ngIf="showDetails" class="details">
        <p>This content is conditionally shown based on parent input!</p>
      </div>
    </div>
  `,
  styles: [`
    .child-demo {
      border: 2px solid #007bff;
      border-radius: 8px;
      padding: 15px;
      margin: 10px 0;
      background: #f8f9fa;
    }
    
    .details {
      background: #e3f2fd;
      padding: 10px;
      border-radius: 4px;
      margin-top: 10px;
    }
    
    h4 {
      margin: 0 0 10px 0;
      color: #007bff;
    }
  `]
})
export class ChildDemoComponent {
  // @Input examples - receiving data from parent
  @Input() message: string = 'No message';
  @Input() count: number = 0;
  @Input() showDetails: boolean = false;
}

// Component demonstrating @Output
@Component({
  selector: 'app-event-demo',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="event-demo">
      <h4>Event Emitter Component</h4>
      <input 
        type="text" 
        [(ngModel)]="inputValue" 
        placeholder="Type something..."
        class="form-control"
      >
      
      <div class="buttons">
        <button class="btn" (click)="sendMessage()">Send Message</button>
        <button class="btn btn-secondary" (click)="sendNumber()">Send Random Number</button>
        <button class="btn btn-danger" (click)="sendAlert()">Send Alert</button>
      </div>
    </div>
  `,
  styles: [`
    .event-demo {
      border: 2px solid #28a745;
      border-radius: 8px;
      padding: 15px;
      margin: 10px 0;
      background: #f8fff8;
    }
    
    .buttons {
      display: flex;
      gap: 10px;
      margin-top: 10px;
      flex-wrap: wrap;
    }
    
    .form-control {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      margin-bottom: 10px;
    }
    
    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    }
    
    .btn {
      background: #007bff;
      color: white;
    }
    
    .btn-secondary {
      background: #6c757d;
    }
    
    .btn-danger {
      background: #dc3545;
    }
    
    h4 {
      margin: 0 0 10px 0;
      color: #28a745;
    }
  `]
})
export class EventDemoComponent {
  inputValue: string = '';

  // @Output examples - emitting events to parent
  @Output() messageEvent = new EventEmitter<string>();
  @Output() numberEvent = new EventEmitter<number>();
  @Output() alertEvent = new EventEmitter<{type: string, message: string}>();

  sendMessage() {
    this.messageEvent.emit(this.inputValue || 'Hello from child!');
  }

  sendNumber() {
    const randomNum = Math.floor(Math.random() * 100) + 1;
    this.numberEvent.emit(randomNum);
  }

  sendAlert() {
    this.alertEvent.emit({
      type: 'warning',
      message: 'This is an alert from the child component!'
    });
  }
}

// Main component demonstrating all decorators
@Component({
  selector: 'app-simple-examples',
  standalone: true,
  imports: [CommonModule, FormsModule, ChildDemoComponent, EventDemoComponent],
  template: `
    <div class="simple-examples">
      <div class="container">
        <h1>Simple Angular Decorators Examples</h1>
        <p>Clear and simple examples of &#64;Input, &#64;Output, and &#64;ViewChild decorators.</p>

        <!-- @Input Example -->
        <section class="example-section">
          <h2>1. &#64;Input Decorator Example</h2>
          <p>Parent component passes data to child component via property binding.</p>
          
          <div class="controls">
            <div class="control-group">
              <label>Message:</label>
              <input 
                type="text" 
                [(ngModel)]="parentMessage" 
                class="form-control"
                placeholder="Enter message for child"
              >
            </div>
            
            <div class="control-group">
              <label>Count:</label>
              <input 
                type="number" 
                [(ngModel)]="parentCount" 
                class="form-control"
              >
            </div>
            
            <div class="control-group">
              <label>
                <input 
                  type="checkbox" 
                  [(ngModel)]="showChildDetails"
                >
                Show Details in Child
              </label>
            </div>
          </div>

          <!-- Child component receiving @Input data -->
          <app-child-demo 
            [message]="parentMessage"
            [count]="parentCount"
            [showDetails]="showChildDetails"
          ></app-child-demo>

          <div class="code-snippet">
            <strong>Code:</strong>
            <pre>// Parent passes data to child
&lt;app-child-demo 
  [message]="parentMessage"
  [count]="parentCount"
  [showDetails]="showChildDetails"
&gt;&lt;/app-child-demo&gt;

// Child receives data
&#64;Input() message: string = 'No message';
&#64;Input() count: number = 0;
&#64;Input() showDetails: boolean = false;</pre>
          </div>
        </section>

        <!-- @Output Example -->
        <section class="example-section">
          <h2>2. &#64;Output Decorator Example</h2>
          <p>Child component emits events to parent component via EventEmitter.</p>
          
          <!-- Child component emitting @Output events -->
          <app-event-demo 
            (messageEvent)="onMessageReceived($event)"
            (numberEvent)="onNumberReceived($event)"
            (alertEvent)="onAlertReceived($event)"
          ></app-event-demo>

          <div class="events-log">
            <h4>Events Received from Child:</h4>
            <div class="log-entries">
              <div *ngFor="let event of eventLog()" class="log-entry">
                <span class="timestamp">{{ event.timestamp | date:'HH:mm:ss' }}</span>
                <span class="event-type">{{ event.type }}</span>
                <span class="event-data">{{ event.data }}</span>
              </div>
            </div>
            <button class="btn btn-secondary" (click)="clearEventLog()">Clear Log</button>
          </div>

          <div class="code-snippet">
            <strong>Code:</strong>
            <pre>// Child emits events
&#64;Output() messageEvent = new EventEmitter&lt;string&gt;();
&#64;Output() numberEvent = new EventEmitter&lt;number&gt;();

sendMessage() {
  this.messageEvent.emit(this.inputValue);
}

// Parent listens to events
&lt;app-event-demo 
  (messageEvent)="onMessageReceived($event)"
  (numberEvent)="onNumberReceived($event)"
&gt;&lt;/app-event-demo&gt;</pre>
          </div>
        </section>

        <!-- @ViewChild Example -->
        <section class="example-section">
          <h2>3. &#64;ViewChild Decorator Example</h2>
          <p>Parent component accesses child elements and components directly.</p>
          
          <div class="viewchild-demo">
            <div class="input-demo">
              <h4>Access Input Element:</h4>
              <input 
                #myInput
                type="text" 
                placeholder="Type something here..."
                class="form-control"
              >
              <div class="buttons">
                <button class="btn" (click)="focusInput()">Focus Input</button>
                <button class="btn btn-secondary" (click)="clearInput()">Clear Input</button>
                <button class="btn btn-secondary" (click)="getInputValue()">Get Value</button>
              </div>
              <p *ngIf="inputValue()">Input Value: <strong>{{ inputValue() }}</strong></p>
            </div>

            <div class="component-demo">
              <h4>Access Child Component:</h4>
              <app-event-demo 
                #childComponent
                (messageEvent)="onMessageReceived($event)"
                (numberEvent)="onNumberReceived($event)"
                (alertEvent)="onAlertReceived($event)"
              ></app-event-demo>
              <div class="buttons">
                <button class="btn" (click)="triggerChildMessage()">Trigger Child Message</button>
                <button class="btn btn-secondary" (click)="setChildInput()">Set Child Input</button>
              </div>
            </div>
          </div>

          <div class="code-snippet">
            <strong>Code:</strong>
            <pre>// Access DOM element
&#64;ViewChild('myInput') inputRef!: ElementRef&lt;HTMLInputElement&gt;;

focusInput() {{ '{' }}
  this.inputRef.nativeElement.focus();
{{ '}' }}

// Access child component
&#64;ViewChild('childComponent') childComp!: EventDemoComponent;

setChildInput() {{ '{' }}
  this.childComp.inputValue = 'Set from parent!';
{{ '}' }}</pre>
          </div>
        </section>
      </div>
    </div>
  `,
  styles: [`
    .simple-examples {
      min-height: 100vh;
      background: #f5f5f5;
      padding: 20px 0;
    }

    .container {
      max-width: 1000px;
      margin: 0 auto;
      padding: 0 20px;
    }

    h1 {
      color: #1976d2;
      text-align: center;
      margin-bottom: 10px;
    }

    .example-section {
      background: white;
      border-radius: 8px;
      padding: 30px;
      margin: 30px 0;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .example-section h2 {
      color: #333;
      border-bottom: 2px solid #007bff;
      padding-bottom: 10px;
      margin-bottom: 20px;
    }

    .controls {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin: 20px 0;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .control-group {
      display: flex;
      flex-direction: column;
      gap: 5px;
    }

    .control-group label {
      font-weight: bold;
      color: #555;
    }

    .form-control {
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
    }

    .events-log {
      margin: 20px 0;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .log-entries {
      max-height: 200px;
      overflow-y: auto;
      margin: 15px 0;
    }

    .log-entry {
      display: flex;
      gap: 15px;
      padding: 8px 0;
      border-bottom: 1px solid #e9ecef;
      font-size: 14px;
    }

    .timestamp {
      color: #6c757d;
      font-weight: bold;
      min-width: 80px;
    }

    .event-type {
      color: #007bff;
      font-weight: bold;
      min-width: 100px;
    }

    .viewchild-demo {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 30px;
      margin: 20px 0;
    }

    .input-demo, .component-demo {
      padding: 20px;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      background: #f8f9fa;
    }

    .buttons {
      display: flex;
      gap: 10px;
      margin: 15px 0;
      flex-wrap: wrap;
    }

    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      background: #007bff;
      color: white;
      transition: background-color 0.3s;
    }

    .btn:hover {
      background: #0056b3;
    }

    .btn-secondary {
      background: #6c757d;
    }

    .btn-secondary:hover {
      background: #545b62;
    }

    .code-snippet {
      margin: 20px 0;
      padding: 20px;
      background: #2d3748;
      color: #e2e8f0;
      border-radius: 8px;
      border-left: 4px solid #28a745;
    }

    .code-snippet pre {
      margin: 10px 0 0 0;
      font-size: 14px;
      line-height: 1.5;
      overflow-x: auto;
    }

    @media (max-width: 768px) {
      .controls {
        grid-template-columns: 1fr;
      }
      
      .viewchild-demo {
        grid-template-columns: 1fr;
      }
      
      .buttons {
        flex-direction: column;
      }
    }
  `]
})
export class SimpleExamplesComponent implements AfterViewInit {
  // @Input example data
  parentMessage = 'Hello from parent!';
  parentCount = 42;
  showChildDetails = true;

  // @Output example data
  eventLog = signal<Array<{timestamp: Date, type: string, data: string}>>([]);

  // @ViewChild examples
  @ViewChild('myInput') inputRef!: ElementRef<HTMLInputElement>;
  @ViewChild('childComponent') childComponent!: EventDemoComponent;

  // Signal for input value
  inputValue = signal('');

  ngAfterViewInit() {
    // ViewChild references are available here
    console.log('ViewChild references are now available');
  }

  // @Output event handlers
  onMessageReceived(message: string) {
    this.addEventLog('Message', message);
  }

  onNumberReceived(number: number) {
    this.addEventLog('Number', number.toString());
  }

  onAlertReceived(alert: {type: string, message: string}) {
    this.addEventLog('Alert', `${alert.type}: ${alert.message}`);
  }

  // @ViewChild methods
  focusInput() {
    this.inputRef.nativeElement.focus();
  }

  clearInput() {
    this.inputRef.nativeElement.value = '';
    this.inputValue.set('');
  }

  getInputValue() {
    const value = this.inputRef.nativeElement.value;
    this.inputValue.set(value);
  }

  triggerChildMessage() {
    this.childComponent.inputValue = 'Triggered from parent!';
    this.childComponent.sendMessage();
  }

  setChildInput() {
    this.childComponent.inputValue = 'Set from parent via ViewChild!';
  }

  // Utility methods
  addEventLog(type: string, data: string) {
    this.eventLog.update(logs => [
      ...logs,
      { timestamp: new Date(), type, data }
    ].slice(-10)); // Keep only last 10 events
  }

  clearEventLog() {
    this.eventLog.set([]);
  }
}
