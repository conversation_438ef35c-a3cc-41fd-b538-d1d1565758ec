import { Component, ViewChild, ElementRef, AfterViewInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TaskFormComponent, Task } from './task-form.component';

@Component({
  selector: 'app-viewchild-demo',
  standalone: true,
  imports: [CommonModule, FormsModule, TaskFormComponent],
  template: `
    <div class="viewchild-demo">
      <div class="card">
        <h2>&#64;ViewChild Examples</h2>
        <p>This component demonstrates different uses of &#64;ViewChild decorator.</p>

        <!-- Example 1: ViewChild with ElementRef -->
        <div class="example-section">
          <h3>1. &#64;ViewChild with ElementRef</h3>
          <div class="input-group">
            <input 
              #textInput
              type="text" 
              placeholder="Type something here..."
              class="form-control"
              (input)="onInputChange()"
            >
            <button class="btn" (click)="focusInput()">Focus Input</button>
            <button class="btn btn-secondary" (click)="clearInput()">Clear</button>
          </div>
          <p class="input-info">
            Input Length: {{ inputLength() }} | 
            Input Value: "{{ inputValue() }}"
          </p>
        </div>

        <!-- Example 2: ViewChild with Component -->
        <div class="example-section">
          <h3>2. &#64;ViewChild with Component Reference</h3>
          <div class="component-controls">
            <button class="btn" (click)="triggerTaskForm()">
              Populate Form with Sample Data
            </button>
            <button class="btn btn-secondary" (click)="resetTaskForm()">
              Reset Task Form
            </button>
          </div>
          
          <app-task-form 
            #taskFormRef
            (taskSubmitted)="onTaskSubmitted($event)"
            (formCancelled)="onFormCancelled()"
            (formReset)="onFormReset()"
          ></app-task-form>
        </div>

        <!-- Example 3: ViewChild with Template Reference -->
        <div class="example-section">
          <h3>3. &#64;ViewChild with Template Reference</h3>
          <div class="canvas-container">
            <canvas 
              #drawingCanvas
              width="400" 
              height="200"
              class="drawing-canvas"
              (mousedown)="startDrawing($event)"
              (mousemove)="draw($event)"
              (mouseup)="stopDrawing()"
              (mouseleave)="stopDrawing()"
            ></canvas>
          </div>
          <div class="canvas-controls">
            <button class="btn" (click)="clearCanvas()">Clear Canvas</button>
            <button class="btn btn-secondary" (click)="drawSample()">Draw Sample</button>
            <input 
              type="color" 
              [(ngModel)]="drawColor" 
              class="color-picker"
            >
            <label>Brush Size:</label>
            <input 
              type="range" 
              min="1" 
              max="20" 
              [(ngModel)]="brushSize"
              class="brush-slider"
            >
            <span>{{ brushSize }}px</span>
          </div>
        </div>

        <!-- Results Display -->
        <div class="results-section">
          <h3>Event Results</h3>
          <div class="results-log">
            <div *ngFor="let log of eventLogs()" class="log-entry">
              <span class="timestamp">{{ log.timestamp | date:'HH:mm:ss' }}</span>
              <span class="event-type">{{ log.type }}</span>
              <span class="event-data">{{ log.data }}</span>
            </div>
          </div>
          <button class="btn btn-secondary" (click)="clearLogs()">Clear Logs</button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .viewchild-demo {
      max-width: 1000px;
      margin: 0 auto;
    }

    .example-section {
      margin: 30px 0;
      padding: 20px;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      background: #f8f9fa;
    }

    .example-section h3 {
      margin: 0 0 15px 0;
      color: #495057;
      border-bottom: 2px solid #007bff;
      padding-bottom: 5px;
    }

    .input-group {
      display: flex;
      gap: 10px;
      margin-bottom: 10px;
    }

    .input-group input {
      flex: 1;
    }

    .input-info {
      font-size: 14px;
      color: #6c757d;
      margin: 10px 0;
    }

    .component-controls {
      margin-bottom: 20px;
      display: flex;
      gap: 10px;
    }

    .canvas-container {
      text-align: center;
      margin: 20px 0;
    }

    .drawing-canvas {
      border: 2px solid #007bff;
      border-radius: 4px;
      cursor: crosshair;
      background: white;
    }

    .canvas-controls {
      display: flex;
      align-items: center;
      gap: 15px;
      justify-content: center;
      margin-top: 15px;
      flex-wrap: wrap;
    }

    .color-picker {
      width: 40px;
      height: 30px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }

    .brush-slider {
      width: 100px;
    }

    .results-section {
      margin-top: 30px;
    }

    .results-log {
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 10px;
      background: white;
      margin: 15px 0;
    }

    .log-entry {
      display: flex;
      gap: 10px;
      padding: 5px 0;
      border-bottom: 1px solid #f1f1f1;
      font-size: 14px;
    }

    .timestamp {
      color: #6c757d;
      font-weight: bold;
      min-width: 80px;
    }

    .event-type {
      color: #007bff;
      font-weight: bold;
      min-width: 120px;
    }

    .event-data {
      color: #333;
    }

    .form-control {
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
    }

    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      background: #007bff;
      color: white;
      transition: background-color 0.3s;
    }

    .btn:hover {
      background: #0056b3;
    }

    .btn-secondary {
      background: #6c757d;
    }

    .btn-secondary:hover {
      background: #545b62;
    }

    h2 {
      color: #1976d2;
      margin-bottom: 20px;
    }

    @media (max-width: 768px) {
      .input-group, .component-controls, .canvas-controls {
        flex-direction: column;
      }
      
      .drawing-canvas {
        width: 100%;
        max-width: 400px;
      }
    }
  `]
})
export class ViewChildDemoComponent implements AfterViewInit {
  // @ViewChild with ElementRef - accessing DOM element
  @ViewChild('textInput') textInputRef!: ElementRef<HTMLInputElement>;
  
  // @ViewChild with Component - accessing component instance
  @ViewChild('taskFormRef') taskFormComponent!: TaskFormComponent;
  
  // @ViewChild with Canvas element
  @ViewChild('drawingCanvas') canvasRef!: ElementRef<HTMLCanvasElement>;

  // Signals for reactive state
  inputLength = signal(0);
  inputValue = signal('');
  eventLogs = signal<Array<{timestamp: Date, type: string, data: string}>>([]);

  // Drawing state
  isDrawing = false;
  drawColor = '#007bff';
  brushSize = 5;

  ngAfterViewInit() {
    // ViewChild elements are available after view initialization
    this.addLog('ViewInit', 'All ViewChild references are now available');
    
    // Initialize canvas context
    this.setupCanvas();
  }

  // Example 1: Working with ElementRef
  focusInput() {
    this.textInputRef.nativeElement.focus();
    this.addLog('ElementRef', 'Input focused programmatically');
  }

  clearInput() {
    this.textInputRef.nativeElement.value = '';
    this.inputValue.set('');
    this.inputLength.set(0);
    this.addLog('ElementRef', 'Input cleared');
  }

  onInputChange() {
    const value = this.textInputRef.nativeElement.value;
    this.inputValue.set(value);
    this.inputLength.set(value.length);
  }

  // Example 2: Working with Component Reference
  triggerTaskForm() {
    const sampleTask: Task = {
      id: 1,
      title: 'Sample Task from ViewChild',
      description: 'This task was populated using @ViewChild component reference',
      priority: 'high',
      completed: false,
      createdAt: new Date()
    };
    
    this.taskFormComponent.editTask(sampleTask);
    this.addLog('Component', 'Task form populated with sample data');
  }

  resetTaskForm() {
    this.taskFormComponent.onReset();
    this.addLog('Component', 'Task form reset via ViewChild');
  }

  // Task form event handlers
  onTaskSubmitted(task: Task) {
    this.addLog('Output Event', `Task submitted: "${task.title}"`);
  }

  onFormCancelled() {
    this.addLog('Output Event', 'Form cancelled');
  }

  onFormReset() {
    this.addLog('Output Event', 'Form reset');
  }

  // Example 3: Working with Canvas ElementRef
  setupCanvas() {
    const canvas = this.canvasRef.nativeElement;
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';
    }
  }

  startDrawing(event: MouseEvent) {
    this.isDrawing = true;
    const rect = this.canvasRef.nativeElement.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    const ctx = this.canvasRef.nativeElement.getContext('2d');
    if (ctx) {
      ctx.beginPath();
      ctx.moveTo(x, y);
    }
  }

  draw(event: MouseEvent) {
    if (!this.isDrawing) return;
    
    const rect = this.canvasRef.nativeElement.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    const ctx = this.canvasRef.nativeElement.getContext('2d');
    if (ctx) {
      ctx.strokeStyle = this.drawColor;
      ctx.lineWidth = this.brushSize;
      ctx.lineTo(x, y);
      ctx.stroke();
    }
  }

  stopDrawing() {
    if (this.isDrawing) {
      this.isDrawing = false;
      this.addLog('Canvas', 'Drawing completed');
    }
  }

  clearCanvas() {
    const canvas = this.canvasRef.nativeElement;
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      this.addLog('Canvas', 'Canvas cleared');
    }
  }

  drawSample() {
    const canvas = this.canvasRef.nativeElement;
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.strokeStyle = '#ff6b6b';
      ctx.lineWidth = 3;
      ctx.beginPath();
      ctx.arc(200, 100, 50, 0, 2 * Math.PI);
      ctx.stroke();
      
      ctx.strokeStyle = '#4ecdc4';
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.rect(150, 50, 100, 100);
      ctx.stroke();
      
      this.addLog('Canvas', 'Sample shapes drawn');
    }
  }

  // Utility methods
  addLog(type: string, data: string) {
    this.eventLogs.update(logs => [
      ...logs,
      { timestamp: new Date(), type, data }
    ].slice(-10)); // Keep only last 10 logs
  }

  clearLogs() {
    this.eventLogs.set([]);
  }
}
