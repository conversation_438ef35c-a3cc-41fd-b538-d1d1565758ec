import{$ as Gt,A as Je,Aa as Me,B as Y,C as Bt,D as xr,Da as tn,E as jr,Ea as rn,F as kr,Fa as nn,G as k,Ga as on,H as $r,Ha as sn,I as b,J as w,L as y,N as zr,Na as an,O as T,P as Vt,Pa as cn,Q as S,R as p,Ra as un,S as Hr,Sa as ln,T as Fr,Ta as dn,U as Ce,V as q,W as Ee,Z as qt,_ as Br,a as d,aa as Vr,ab as hn,b as M,ba as qr,bb as et,c as _r,ca as te,cb as fn,d as Nr,da as ue,db as tt,e as jt,ea as Wt,f as kt,fa as Gr,g as B,ga as Zt,gb as P,h as U,hb as Ae,i as V,ia as Wr,ib as pn,j as D,ja as Yt,jb as gn,k as h,ka as Zr,kb as vn,l as we,la as be,lb as rt,m as Ur,ma as Qt,n as Pr,na as Yr,o as v,oa as Te,ob as mn,p as $t,pa as Qr,pb as yn,q as O,qb as Jt,r as zt,ra as Ie,rb as Sn,s as Lr,sa as Kr,t as Ht,ta as Kt,ua as L,v as J,va as Xr,w as ee,wa as Jr,x as ae,xa as Xt,y as Ft,ya as en,z as ce}from"./chunk-XF3IHJ6T.js";var st=new T(""),nr=(()=>{class t{_zone;_plugins;_eventNameToPlugin=new Map;constructor(e,r){this._zone=r,e.forEach(i=>{i.manager=this}),this._plugins=e.slice().reverse()}addEventListener(e,r,i,o){return this._findPluginFor(r).addEventListener(e,r,i,o)}getZone(){return this._zone}_findPluginFor(e){let r=this._eventNameToPlugin.get(e);if(r)return r;if(r=this._plugins.find(o=>o.supports(e)),!r)throw new w(5101,!1);return this._eventNameToPlugin.set(e,r),r}static \u0275fac=function(r){return new(r||t)(S(st),S(ue))};static \u0275prov=y({token:t,factory:t.\u0275fac})}return t})(),De=class{_doc;constructor(n){this._doc=n}manager},nt="ng-app-id";function Rn(t){for(let n of t)n.remove()}function wn(t,n){let e=n.createElement("style");return e.textContent=t,e}function Ci(t,n,e,r){let i=t.head?.querySelectorAll(`style[${nt}="${n}"],link[${nt}="${n}"]`);if(i)for(let o of i)o.removeAttribute(nt),o instanceof HTMLLinkElement?r.set(o.href.slice(o.href.lastIndexOf("/")+1),{usage:0,elements:[o]}):o.textContent&&e.set(o.textContent,{usage:0,elements:[o]})}function tr(t,n){let e=n.createElement("link");return e.setAttribute("rel","stylesheet"),e.setAttribute("href",t),e}var ir=(()=>{class t{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(e,r,i,o={}){this.doc=e,this.appId=r,this.nonce=i,this.isServer=Jt(o),Ci(e,r,this.inline,this.external),this.hosts.add(e.head)}addStyles(e,r){for(let i of e)this.addUsage(i,this.inline,wn);r?.forEach(i=>this.addUsage(i,this.external,tr))}removeStyles(e,r){for(let i of e)this.removeUsage(i,this.inline);r?.forEach(i=>this.removeUsage(i,this.external))}addUsage(e,r,i){let o=r.get(e);o?o.usage++:r.set(e,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,i(e,this.doc)))})}removeUsage(e,r){let i=r.get(e);i&&(i.usage--,i.usage<=0&&(Rn(i.elements),r.delete(e)))}ngOnDestroy(){for(let[,{elements:e}]of[...this.inline,...this.external])Rn(e);this.hosts.clear()}addHost(e){this.hosts.add(e);for(let[r,{elements:i}]of this.inline)i.push(this.addElement(e,wn(r,this.doc)));for(let[r,{elements:i}]of this.external)i.push(this.addElement(e,tr(r,this.doc)))}removeHost(e){this.hosts.delete(e)}addElement(e,r){return this.nonce&&r.setAttribute("nonce",this.nonce),this.isServer&&r.setAttribute(nt,this.appId),e.appendChild(r)}static \u0275fac=function(r){return new(r||t)(S(P),S(Yt),S(Qt,8),S(be))};static \u0275prov=y({token:t,factory:t.\u0275fac})}return t})(),er={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},or=/%COMP%/g;var En="%COMP%",Ei=`_nghost-${En}`,bi=`_ngcontent-${En}`,Ti=!0,Ii=new T("",{providedIn:"root",factory:()=>Ti});function Mi(t){return bi.replace(or,t)}function Ai(t){return Ei.replace(or,t)}function bn(t,n){return n.map(e=>e.replace(or,t))}var sr=(()=>{class t{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(e,r,i,o,s,a,c,u=null,l=null){this.eventManager=e,this.sharedStylesHost=r,this.appId=i,this.removeStylesOnCompDestroy=o,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=u,this.tracingService=l,this.platformIsServer=Jt(a),this.defaultRenderer=new Oe(e,s,c,this.platformIsServer,this.tracingService)}createRenderer(e,r){if(!e||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===Te.ShadowDom&&(r=M(d({},r),{encapsulation:Te.Emulated}));let i=this.getOrCreateRenderer(e,r);return i instanceof it?i.applyToHost(e):i instanceof _e&&i.applyStyles(),i}getOrCreateRenderer(e,r){let i=this.rendererByCompId,o=i.get(r.id);if(!o){let s=this.doc,a=this.ngZone,c=this.eventManager,u=this.sharedStylesHost,l=this.removeStylesOnCompDestroy,m=this.platformIsServer,R=this.tracingService;switch(r.encapsulation){case Te.Emulated:o=new it(c,u,r,this.appId,l,s,a,m,R);break;case Te.ShadowDom:return new rr(c,u,e,r,s,a,this.nonce,m,R);default:o=new _e(c,u,r,l,s,a,m,R);break}i.set(r.id,o)}return o}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(e){this.rendererByCompId.delete(e)}static \u0275fac=function(r){return new(r||t)(S(nr),S(ir),S(Yt),S(Ii),S(P),S(be),S(ue),S(Qt),S(Yr,8))};static \u0275prov=y({token:t,factory:t.\u0275fac})}return t})(),Oe=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(n,e,r,i,o){this.eventManager=n,this.doc=e,this.ngZone=r,this.platformIsServer=i,this.tracingService=o}destroy(){}destroyNode=null;createElement(n,e){return e?this.doc.createElementNS(er[e]||e,n):this.doc.createElement(n)}createComment(n){return this.doc.createComment(n)}createText(n){return this.doc.createTextNode(n)}appendChild(n,e){(Cn(n)?n.content:n).appendChild(e)}insertBefore(n,e,r){n&&(Cn(n)?n.content:n).insertBefore(e,r)}removeChild(n,e){e.remove()}selectRootElement(n,e){let r=typeof n=="string"?this.doc.querySelector(n):n;if(!r)throw new w(-5104,!1);return e||(r.textContent=""),r}parentNode(n){return n.parentNode}nextSibling(n){return n.nextSibling}setAttribute(n,e,r,i){if(i){e=i+":"+e;let o=er[i];o?n.setAttributeNS(o,e,r):n.setAttribute(e,r)}else n.setAttribute(e,r)}removeAttribute(n,e,r){if(r){let i=er[r];i?n.removeAttributeNS(i,e):n.removeAttribute(`${r}:${e}`)}else n.removeAttribute(e)}addClass(n,e){n.classList.add(e)}removeClass(n,e){n.classList.remove(e)}setStyle(n,e,r,i){i&(Ie.DashCase|Ie.Important)?n.style.setProperty(e,r,i&Ie.Important?"important":""):n.style[e]=r}removeStyle(n,e,r){r&Ie.DashCase?n.style.removeProperty(e):n.style[e]=""}setProperty(n,e,r){n!=null&&(n[e]=r)}setValue(n,e){n.nodeValue=e}listen(n,e,r,i){if(typeof n=="string"&&(n=Ae().getGlobalEventTarget(this.doc,n),!n))throw new w(5102,!1);let o=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(o=this.tracingService.wrapEventListener(n,e,o)),this.eventManager.addEventListener(n,e,o,i)}decoratePreventDefault(n){return e=>{if(e==="__ngUnwrap__")return n;(this.platformIsServer?this.ngZone.runGuarded(()=>n(e)):n(e))===!1&&e.preventDefault()}}};function Cn(t){return t.tagName==="TEMPLATE"&&t.content!==void 0}var rr=class extends Oe{sharedStylesHost;hostEl;shadowRoot;constructor(n,e,r,i,o,s,a,c,u){super(n,o,s,c,u),this.sharedStylesHost=e,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let l=i.styles;l=bn(i.id,l);for(let R of l){let E=document.createElement("style");a&&E.setAttribute("nonce",a),E.textContent=R,this.shadowRoot.appendChild(E)}let m=i.getExternalStyles?.();if(m)for(let R of m){let E=tr(R,o);a&&E.setAttribute("nonce",a),this.shadowRoot.appendChild(E)}}nodeOrShadowRoot(n){return n===this.hostEl?this.shadowRoot:n}appendChild(n,e){return super.appendChild(this.nodeOrShadowRoot(n),e)}insertBefore(n,e,r){return super.insertBefore(this.nodeOrShadowRoot(n),e,r)}removeChild(n,e){return super.removeChild(null,e)}parentNode(n){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(n)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},_e=class extends Oe{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(n,e,r,i,o,s,a,c,u){super(n,o,s,a,c),this.sharedStylesHost=e,this.removeStylesOnCompDestroy=i;let l=r.styles;this.styles=u?bn(u,l):l,this.styleUrls=r.getExternalStyles?.(u)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},it=class extends _e{contentAttr;hostAttr;constructor(n,e,r,i,o,s,a,c,u){let l=i+"-"+r.id;super(n,e,r,o,s,a,c,u,l),this.contentAttr=Mi(l),this.hostAttr=Ai(l)}applyToHost(n){this.applyStyles(),this.setAttribute(n,this.hostAttr,"")}createElement(n,e){let r=super.createElement(n,e);return super.setAttribute(r,this.contentAttr,""),r}};var at=class t extends gn{supportsDOMEvents=!0;static makeCurrent(){pn(new t)}onAndCancel(n,e,r,i){return n.addEventListener(e,r,i),()=>{n.removeEventListener(e,r,i)}}dispatchEvent(n,e){n.dispatchEvent(e)}remove(n){n.remove()}createElement(n,e){return e=e||this.getDefaultDocument(),e.createElement(n)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(n){return n.nodeType===Node.ELEMENT_NODE}isShadowRoot(n){return n instanceof DocumentFragment}getGlobalEventTarget(n,e){return e==="window"?window:e==="document"?n:e==="body"?n.body:null}getBaseHref(n){let e=Di();return e==null?null:Oi(e)}resetBaseElement(){Ne=null}getUserAgent(){return window.navigator.userAgent}getCookie(n){return mn(document.cookie,n)}},Ne=null;function Di(){return Ne=Ne||document.head.querySelector("base"),Ne?Ne.getAttribute("href"):null}function Oi(t){return new URL(t,document.baseURI).pathname}var _i=(()=>{class t{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||t)};static \u0275prov=y({token:t,factory:t.\u0275fac})}return t})(),An=(()=>{class t extends De{constructor(e){super(e)}supports(e){return!0}addEventListener(e,r,i,o){return e.addEventListener(r,i,o),()=>this.removeEventListener(e,r,i,o)}removeEventListener(e,r,i,o){return e.removeEventListener(r,i,o)}static \u0275fac=function(r){return new(r||t)(S(P))};static \u0275prov=y({token:t,factory:t.\u0275fac})}return t})(),Tn=["alt","control","meta","shift"],Ni={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},Ui={alt:t=>t.altKey,control:t=>t.ctrlKey,meta:t=>t.metaKey,shift:t=>t.shiftKey},Dn=(()=>{class t extends De{constructor(e){super(e)}supports(e){return t.parseEventName(e)!=null}addEventListener(e,r,i,o){let s=t.parseEventName(r),a=t.eventCallback(s.fullKey,i,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>Ae().onAndCancel(e,s.domEventName,a,o))}static parseEventName(e){let r=e.toLowerCase().split("."),i=r.shift();if(r.length===0||!(i==="keydown"||i==="keyup"))return null;let o=t._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),Tn.forEach(u=>{let l=r.indexOf(u);l>-1&&(r.splice(l,1),s+=u+".")}),s+=o,r.length!=0||o.length===0)return null;let c={};return c.domEventName=i,c.fullKey=s,c}static matchEventFullKeyCode(e,r){let i=Ni[e.key]||e.key,o="";return r.indexOf("code.")>-1&&(i=e.code,o="code."),i==null||!i?!1:(i=i.toLowerCase(),i===" "?i="space":i==="."&&(i="dot"),Tn.forEach(s=>{if(s!==i){let a=Ui[s];a(e)&&(o+=s+".")}}),o+=i,o===r)}static eventCallback(e,r,i){return o=>{t.matchEventFullKeyCode(o,e)&&i.runGuarded(()=>r(o))}}static _normalizeKey(e){return e==="esc"?"escape":e}static \u0275fac=function(r){return new(r||t)(S(P))};static \u0275prov=y({token:t,factory:t.\u0275fac})}return t})();function Pi(t,n){return fn(d({rootComponent:t},Li(n)))}function Li(t){return{appProviders:[...zi,...t?.providers??[]],platformProviders:$i}}function xi(){at.makeCurrent()}function ji(){return new Wt}function ki(){return Wr(document),document}var $i=[{provide:be,useValue:yn},{provide:Zr,useValue:xi,multi:!0},{provide:P,useFactory:ki}];var zi=[{provide:Fr,useValue:"root"},{provide:Wt,useFactory:ji},{provide:st,useClass:An,multi:!0,deps:[P]},{provide:st,useClass:Dn,multi:!0,deps:[P]},sr,ir,nr,{provide:Kr,useExisting:sr},{provide:Sn,useClass:_i},[]];var On=(()=>{class t{_doc;constructor(e){this._doc=e}getTitle(){return this._doc.title}setTitle(e){this._doc.title=e||""}static \u0275fac=function(r){return new(r||t)(S(P))};static \u0275prov=y({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();var f="primary",Ge=Symbol("RouteTitle"),dr=class{params;constructor(n){this.params=n||{}}has(n){return Object.prototype.hasOwnProperty.call(this.params,n)}get(n){if(this.has(n)){let e=this.params[n];return Array.isArray(e)?e[0]:e}return null}getAll(n){if(this.has(n)){let e=this.params[n];return Array.isArray(e)?e:[e]}return[]}get keys(){return Object.keys(this.params)}};function ie(t){return new dr(t)}function kn(t,n,e){let r=e.path.split("/");if(r.length>t.length||e.pathMatch==="full"&&(n.hasChildren()||r.length<t.length))return null;let i={};for(let o=0;o<r.length;o++){let s=r[o],a=t[o];if(s[0]===":")i[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:t.slice(0,r.length),posParams:i}}function Fi(t,n){if(t.length!==n.length)return!1;for(let e=0;e<t.length;++e)if(!$(t[e],n[e]))return!1;return!0}function $(t,n){let e=t?hr(t):void 0,r=n?hr(n):void 0;if(!e||!r||e.length!=r.length)return!1;let i;for(let o=0;o<e.length;o++)if(i=e[o],!$n(t[i],n[i]))return!1;return!0}function hr(t){return[...Object.keys(t),...Object.getOwnPropertySymbols(t)]}function $n(t,n){if(Array.isArray(t)&&Array.isArray(n)){if(t.length!==n.length)return!1;let e=[...t].sort(),r=[...n].sort();return e.every((i,o)=>r[o]===i)}else return t===n}function zn(t){return t.length>0?t[t.length-1]:null}function X(t){return Ur(t)?t:rn(t)?D(Promise.resolve(t)):h(t)}var Bi={exact:Fn,subset:Bn},Hn={exact:Vi,subset:qi,ignored:()=>!0};function _n(t,n,e){return Bi[e.paths](t.root,n.root,e.matrixParams)&&Hn[e.queryParams](t.queryParams,n.queryParams)&&!(e.fragment==="exact"&&t.fragment!==n.fragment)}function Vi(t,n){return $(t,n)}function Fn(t,n,e){if(!re(t.segments,n.segments)||!lt(t.segments,n.segments,e)||t.numberOfChildren!==n.numberOfChildren)return!1;for(let r in n.children)if(!t.children[r]||!Fn(t.children[r],n.children[r],e))return!1;return!0}function qi(t,n){return Object.keys(n).length<=Object.keys(t).length&&Object.keys(n).every(e=>$n(t[e],n[e]))}function Bn(t,n,e){return Vn(t,n,n.segments,e)}function Vn(t,n,e,r){if(t.segments.length>e.length){let i=t.segments.slice(0,e.length);return!(!re(i,e)||n.hasChildren()||!lt(i,e,r))}else if(t.segments.length===e.length){if(!re(t.segments,e)||!lt(t.segments,e,r))return!1;for(let i in n.children)if(!t.children[i]||!Bn(t.children[i],n.children[i],r))return!1;return!0}else{let i=e.slice(0,t.segments.length),o=e.slice(t.segments.length);return!re(t.segments,i)||!lt(t.segments,i,r)||!t.children[f]?!1:Vn(t.children[f],n,o,r)}}function lt(t,n,e){return n.every((r,i)=>Hn[e](t[i].parameters,r.parameters))}var H=class{root;queryParams;fragment;_queryParamMap;constructor(n=new g([],{}),e={},r=null){this.root=n,this.queryParams=e,this.fragment=r}get queryParamMap(){return this._queryParamMap??=ie(this.queryParams),this._queryParamMap}toString(){return Zi.serialize(this)}},g=class{segments;children;parent=null;constructor(n,e){this.segments=n,this.children=e,Object.values(e).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return dt(this)}},Q=class{path;parameters;_parameterMap;constructor(n,e){this.path=n,this.parameters=e}get parameterMap(){return this._parameterMap??=ie(this.parameters),this._parameterMap}toString(){return Gn(this)}};function Gi(t,n){return re(t,n)&&t.every((e,r)=>$(e.parameters,n[r].parameters))}function re(t,n){return t.length!==n.length?!1:t.every((e,r)=>e.path===n[r].path)}function Wi(t,n){let e=[];return Object.entries(t.children).forEach(([r,i])=>{r===f&&(e=e.concat(n(i,r)))}),Object.entries(t.children).forEach(([r,i])=>{r!==f&&(e=e.concat(n(i,r)))}),e}var We=(()=>{class t{static \u0275fac=function(r){return new(r||t)};static \u0275prov=y({token:t,factory:()=>new oe,providedIn:"root"})}return t})(),oe=class{parse(n){let e=new pr(n);return new H(e.parseRootSegment(),e.parseQueryParams(),e.parseFragment())}serialize(n){let e=`/${Ue(n.root,!0)}`,r=Ki(n.queryParams),i=typeof n.fragment=="string"?`#${Yi(n.fragment)}`:"";return`${e}${r}${i}`}},Zi=new oe;function dt(t){return t.segments.map(n=>Gn(n)).join("/")}function Ue(t,n){if(!t.hasChildren())return dt(t);if(n){let e=t.children[f]?Ue(t.children[f],!1):"",r=[];return Object.entries(t.children).forEach(([i,o])=>{i!==f&&r.push(`${i}:${Ue(o,!1)}`)}),r.length>0?`${e}(${r.join("//")})`:e}else{let e=Wi(t,(r,i)=>i===f?[Ue(t.children[f],!1)]:[`${i}:${Ue(r,!1)}`]);return Object.keys(t.children).length===1&&t.children[f]!=null?`${dt(t)}/${e[0]}`:`${dt(t)}/(${e.join("//")})`}}function qn(t){return encodeURIComponent(t).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function ct(t){return qn(t).replace(/%3B/gi,";")}function Yi(t){return encodeURI(t)}function fr(t){return qn(t).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function ht(t){return decodeURIComponent(t)}function Nn(t){return ht(t.replace(/\+/g,"%20"))}function Gn(t){return`${fr(t.path)}${Qi(t.parameters)}`}function Qi(t){return Object.entries(t).map(([n,e])=>`;${fr(n)}=${fr(e)}`).join("")}function Ki(t){let n=Object.entries(t).map(([e,r])=>Array.isArray(r)?r.map(i=>`${ct(e)}=${ct(i)}`).join("&"):`${ct(e)}=${ct(r)}`).filter(e=>e);return n.length?`?${n.join("&")}`:""}var Xi=/^[^\/()?;#]+/;function ar(t){let n=t.match(Xi);return n?n[0]:""}var Ji=/^[^\/()?;=#]+/;function eo(t){let n=t.match(Ji);return n?n[0]:""}var to=/^[^=?&#]+/;function ro(t){let n=t.match(to);return n?n[0]:""}var no=/^[^&#]+/;function io(t){let n=t.match(no);return n?n[0]:""}var pr=class{url;remaining;constructor(n){this.url=n,this.remaining=n}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new g([],{}):new g([],this.parseChildren())}parseQueryParams(){let n={};if(this.consumeOptional("?"))do this.parseQueryParam(n);while(this.consumeOptional("&"));return n}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let n=[];for(this.peekStartsWith("(")||n.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),n.push(this.parseSegment());let e={};this.peekStartsWith("/(")&&(this.capture("/"),e=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(n.length>0||Object.keys(e).length>0)&&(r[f]=new g(n,e)),r}parseSegment(){let n=ar(this.remaining);if(n===""&&this.peekStartsWith(";"))throw new w(4009,!1);return this.capture(n),new Q(ht(n),this.parseMatrixParams())}parseMatrixParams(){let n={};for(;this.consumeOptional(";");)this.parseParam(n);return n}parseParam(n){let e=eo(this.remaining);if(!e)return;this.capture(e);let r="";if(this.consumeOptional("=")){let i=ar(this.remaining);i&&(r=i,this.capture(r))}n[ht(e)]=ht(r)}parseQueryParam(n){let e=ro(this.remaining);if(!e)return;this.capture(e);let r="";if(this.consumeOptional("=")){let s=io(this.remaining);s&&(r=s,this.capture(r))}let i=Nn(e),o=Nn(r);if(n.hasOwnProperty(i)){let s=n[i];Array.isArray(s)||(s=[s],n[i]=s),s.push(o)}else n[i]=o}parseParens(n){let e={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=ar(this.remaining),i=this.remaining[r.length];if(i!=="/"&&i!==")"&&i!==";")throw new w(4010,!1);let o;r.indexOf(":")>-1?(o=r.slice(0,r.indexOf(":")),this.capture(o),this.capture(":")):n&&(o=f);let s=this.parseChildren();e[o]=Object.keys(s).length===1?s[f]:new g([],s),this.consumeOptional("//")}return e}peekStartsWith(n){return this.remaining.startsWith(n)}consumeOptional(n){return this.peekStartsWith(n)?(this.remaining=this.remaining.substring(n.length),!0):!1}capture(n){if(!this.consumeOptional(n))throw new w(4011,!1)}};function Wn(t){return t.segments.length>0?new g([],{[f]:t}):t}function Zn(t){let n={};for(let[r,i]of Object.entries(t.children)){let o=Zn(i);if(r===f&&o.segments.length===0&&o.hasChildren())for(let[s,a]of Object.entries(o.children))n[s]=a;else(o.segments.length>0||o.hasChildren())&&(n[r]=o)}let e=new g(t.segments,n);return oo(e)}function oo(t){if(t.numberOfChildren===1&&t.children[f]){let n=t.children[f];return new g(t.segments.concat(n.segments),n.children)}return t}function K(t){return t instanceof H}function Yn(t,n,e=null,r=null){let i=Qn(t);return Kn(i,n,e,r)}function Qn(t){let n;function e(o){let s={};for(let c of o.children){let u=e(c);s[c.outlet]=u}let a=new g(o.url,s);return o===t&&(n=a),a}let r=e(t.root),i=Wn(r);return n??i}function Kn(t,n,e,r){let i=t;for(;i.parent;)i=i.parent;if(n.length===0)return cr(i,i,i,e,r);let o=so(n);if(o.toRoot())return cr(i,i,new g([],{}),e,r);let s=ao(o,i,t),a=s.processChildren?Le(s.segmentGroup,s.index,o.commands):Jn(s.segmentGroup,s.index,o.commands);return cr(i,s.segmentGroup,a,e,r)}function pt(t){return typeof t=="object"&&t!=null&&!t.outlets&&!t.segmentPath}function je(t){return typeof t=="object"&&t!=null&&t.outlets}function cr(t,n,e,r,i){let o={};r&&Object.entries(r).forEach(([c,u])=>{o[c]=Array.isArray(u)?u.map(l=>`${l}`):`${u}`});let s;t===n?s=e:s=Xn(t,n,e);let a=Wn(Zn(s));return new H(a,o,i)}function Xn(t,n,e){let r={};return Object.entries(t.children).forEach(([i,o])=>{o===n?r[i]=e:r[i]=Xn(o,n,e)}),new g(t.segments,r)}var gt=class{isAbsolute;numberOfDoubleDots;commands;constructor(n,e,r){if(this.isAbsolute=n,this.numberOfDoubleDots=e,this.commands=r,n&&r.length>0&&pt(r[0]))throw new w(4003,!1);let i=r.find(je);if(i&&i!==zn(r))throw new w(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function so(t){if(typeof t[0]=="string"&&t.length===1&&t[0]==="/")return new gt(!0,0,t);let n=0,e=!1,r=t.reduce((i,o,s)=>{if(typeof o=="object"&&o!=null){if(o.outlets){let a={};return Object.entries(o.outlets).forEach(([c,u])=>{a[c]=typeof u=="string"?u.split("/"):u}),[...i,{outlets:a}]}if(o.segmentPath)return[...i,o.segmentPath]}return typeof o!="string"?[...i,o]:s===0?(o.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?e=!0:a===".."?n++:a!=""&&i.push(a))}),i):[...i,o]},[]);return new gt(e,n,r)}var he=class{segmentGroup;processChildren;index;constructor(n,e,r){this.segmentGroup=n,this.processChildren=e,this.index=r}};function ao(t,n,e){if(t.isAbsolute)return new he(n,!0,0);if(!e)return new he(n,!1,NaN);if(e.parent===null)return new he(e,!0,0);let r=pt(t.commands[0])?0:1,i=e.segments.length-1+r;return co(e,i,t.numberOfDoubleDots)}function co(t,n,e){let r=t,i=n,o=e;for(;o>i;){if(o-=i,r=r.parent,!r)throw new w(4005,!1);i=r.segments.length}return new he(r,!1,i-o)}function uo(t){return je(t[0])?t[0].outlets:{[f]:t}}function Jn(t,n,e){if(t??=new g([],{}),t.segments.length===0&&t.hasChildren())return Le(t,n,e);let r=lo(t,n,e),i=e.slice(r.commandIndex);if(r.match&&r.pathIndex<t.segments.length){let o=new g(t.segments.slice(0,r.pathIndex),{});return o.children[f]=new g(t.segments.slice(r.pathIndex),t.children),Le(o,0,i)}else return r.match&&i.length===0?new g(t.segments,{}):r.match&&!t.hasChildren()?gr(t,n,e):r.match?Le(t,0,i):gr(t,n,e)}function Le(t,n,e){if(e.length===0)return new g(t.segments,{});{let r=uo(e),i={};if(Object.keys(r).some(o=>o!==f)&&t.children[f]&&t.numberOfChildren===1&&t.children[f].segments.length===0){let o=Le(t.children[f],n,e);return new g(t.segments,o.children)}return Object.entries(r).forEach(([o,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(i[o]=Jn(t.children[o],n,s))}),Object.entries(t.children).forEach(([o,s])=>{r[o]===void 0&&(i[o]=s)}),new g(t.segments,i)}}function lo(t,n,e){let r=0,i=n,o={match:!1,pathIndex:0,commandIndex:0};for(;i<t.segments.length;){if(r>=e.length)return o;let s=t.segments[i],a=e[r];if(je(a))break;let c=`${a}`,u=r<e.length-1?e[r+1]:null;if(i>0&&c===void 0)break;if(c&&u&&typeof u=="object"&&u.outlets===void 0){if(!Pn(c,u,s))return o;r+=2}else{if(!Pn(c,{},s))return o;r++}i++}return{match:!0,pathIndex:i,commandIndex:r}}function gr(t,n,e){let r=t.segments.slice(0,n),i=0;for(;i<e.length;){let o=e[i];if(je(o)){let c=ho(o.outlets);return new g(r,c)}if(i===0&&pt(e[0])){let c=t.segments[n];r.push(new Q(c.path,Un(e[0]))),i++;continue}let s=je(o)?o.outlets[f]:`${o}`,a=i<e.length-1?e[i+1]:null;s&&a&&pt(a)?(r.push(new Q(s,Un(a))),i+=2):(r.push(new Q(s,{})),i++)}return new g(r,{})}function ho(t){let n={};return Object.entries(t).forEach(([e,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(n[e]=gr(new g([],{}),0,r))}),n}function Un(t){let n={};return Object.entries(t).forEach(([e,r])=>n[e]=`${r}`),n}function Pn(t,n,e){return t==e.path&&$(n,e.parameters)}var ft="imperative",C=function(t){return t[t.NavigationStart=0]="NavigationStart",t[t.NavigationEnd=1]="NavigationEnd",t[t.NavigationCancel=2]="NavigationCancel",t[t.NavigationError=3]="NavigationError",t[t.RoutesRecognized=4]="RoutesRecognized",t[t.ResolveStart=5]="ResolveStart",t[t.ResolveEnd=6]="ResolveEnd",t[t.GuardsCheckStart=7]="GuardsCheckStart",t[t.GuardsCheckEnd=8]="GuardsCheckEnd",t[t.RouteConfigLoadStart=9]="RouteConfigLoadStart",t[t.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",t[t.ChildActivationStart=11]="ChildActivationStart",t[t.ChildActivationEnd=12]="ChildActivationEnd",t[t.ActivationStart=13]="ActivationStart",t[t.ActivationEnd=14]="ActivationEnd",t[t.Scroll=15]="Scroll",t[t.NavigationSkipped=16]="NavigationSkipped",t}(C||{}),N=class{id;url;constructor(n,e){this.id=n,this.url=e}},se=class extends N{type=C.NavigationStart;navigationTrigger;restoredState;constructor(n,e,r="imperative",i=null){super(n,e),this.navigationTrigger=r,this.restoredState=i}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},j=class extends N{urlAfterRedirects;type=C.NavigationEnd;constructor(n,e,r){super(n,e),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},A=function(t){return t[t.Redirect=0]="Redirect",t[t.SupersededByNewNavigation=1]="SupersededByNewNavigation",t[t.NoDataFromResolver=2]="NoDataFromResolver",t[t.GuardRejected=3]="GuardRejected",t}(A||{}),ke=function(t){return t[t.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",t[t.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",t}(ke||{}),z=class extends N{reason;code;type=C.NavigationCancel;constructor(n,e,r,i){super(n,e),this.reason=r,this.code=i}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},G=class extends N{reason;code;type=C.NavigationSkipped;constructor(n,e,r,i){super(n,e),this.reason=r,this.code=i}},pe=class extends N{error;target;type=C.NavigationError;constructor(n,e,r,i){super(n,e),this.error=r,this.target=i}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},$e=class extends N{urlAfterRedirects;state;type=C.RoutesRecognized;constructor(n,e,r,i){super(n,e),this.urlAfterRedirects=r,this.state=i}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},vt=class extends N{urlAfterRedirects;state;type=C.GuardsCheckStart;constructor(n,e,r,i){super(n,e),this.urlAfterRedirects=r,this.state=i}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},mt=class extends N{urlAfterRedirects;state;shouldActivate;type=C.GuardsCheckEnd;constructor(n,e,r,i,o){super(n,e),this.urlAfterRedirects=r,this.state=i,this.shouldActivate=o}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},yt=class extends N{urlAfterRedirects;state;type=C.ResolveStart;constructor(n,e,r,i){super(n,e),this.urlAfterRedirects=r,this.state=i}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},St=class extends N{urlAfterRedirects;state;type=C.ResolveEnd;constructor(n,e,r,i){super(n,e),this.urlAfterRedirects=r,this.state=i}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Rt=class{route;type=C.RouteConfigLoadStart;constructor(n){this.route=n}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},wt=class{route;type=C.RouteConfigLoadEnd;constructor(n){this.route=n}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},Ct=class{snapshot;type=C.ChildActivationStart;constructor(n){this.snapshot=n}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Et=class{snapshot;type=C.ChildActivationEnd;constructor(n){this.snapshot=n}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},bt=class{snapshot;type=C.ActivationStart;constructor(n){this.snapshot=n}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Tt=class{snapshot;type=C.ActivationEnd;constructor(n){this.snapshot=n}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}};var ze=class{},ge=class{url;navigationBehaviorOptions;constructor(n,e){this.url=n,this.navigationBehaviorOptions=e}};function fo(t,n){return t.providers&&!t._injector&&(t._injector=Xt(t.providers,n,`Route: ${t.path}`)),t._injector??n}function x(t){return t.outlet||f}function po(t,n){let e=t.filter(r=>x(r)===n);return e.push(...t.filter(r=>x(r)!==n)),e}function Ze(t){if(!t)return null;if(t.routeConfig?._injector)return t.routeConfig._injector;for(let n=t.parent;n;n=n.parent){let e=n.routeConfig;if(e?._loadedInjector)return e._loadedInjector;if(e?._injector)return e._injector}return null}var It=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return Ze(this.route?.snapshot)??this.rootInjector}constructor(n){this.rootInjector=n,this.children=new ye(this.rootInjector)}},ye=(()=>{class t{rootInjector;contexts=new Map;constructor(e){this.rootInjector=e}onChildOutletCreated(e,r){let i=this.getOrCreateContext(e);i.outlet=r,this.contexts.set(e,i)}onChildOutletDestroyed(e){let r=this.getContext(e);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let e=this.contexts;return this.contexts=new Map,e}onOutletReAttached(e){this.contexts=e}getOrCreateContext(e){let r=this.getContext(e);return r||(r=new It(this.rootInjector),this.contexts.set(e,r)),r}getContext(e){return this.contexts.get(e)||null}static \u0275fac=function(r){return new(r||t)(S(Ce))};static \u0275prov=y({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),Mt=class{_root;constructor(n){this._root=n}get root(){return this._root.value}parent(n){let e=this.pathFromRoot(n);return e.length>1?e[e.length-2]:null}children(n){let e=vr(n,this._root);return e?e.children.map(r=>r.value):[]}firstChild(n){let e=vr(n,this._root);return e&&e.children.length>0?e.children[0].value:null}siblings(n){let e=mr(n,this._root);return e.length<2?[]:e[e.length-2].children.map(i=>i.value).filter(i=>i!==n)}pathFromRoot(n){return mr(n,this._root).map(e=>e.value)}};function vr(t,n){if(t===n.value)return n;for(let e of n.children){let r=vr(t,e);if(r)return r}return null}function mr(t,n){if(t===n.value)return[n];for(let e of n.children){let r=mr(t,e);if(r.length)return r.unshift(n),r}return[]}var _=class{value;children;constructor(n,e){this.value=n,this.children=e}toString(){return`TreeNode(${this.value})`}};function de(t){let n={};return t&&t.children.forEach(e=>n[e.value.outlet]=e),n}var He=class extends Mt{snapshot;constructor(n,e){super(n),this.snapshot=e,Tr(this,n)}toString(){return this.snapshot.toString()}};function ei(t){let n=go(t),e=new U([new Q("",{})]),r=new U({}),i=new U({}),o=new U({}),s=new U(""),a=new W(e,r,o,s,i,f,t,n.root);return a.snapshot=n.root,new He(new _(a,[]),n)}function go(t){let n={},e={},r={},i="",o=new ne([],n,r,i,e,f,t,null,{});return new Fe("",new _(o,[]))}var W=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(n,e,r,i,o,s,a,c){this.urlSubject=n,this.paramsSubject=e,this.queryParamsSubject=r,this.fragmentSubject=i,this.dataSubject=o,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(v(u=>u[Ge]))??h(void 0),this.url=n,this.params=e,this.queryParams=r,this.fragment=i,this.data=o}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(v(n=>ie(n))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(v(n=>ie(n))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function At(t,n,e="emptyOnly"){let r,{routeConfig:i}=t;return n!==null&&(e==="always"||i?.path===""||!n.component&&!n.routeConfig?.loadComponent)?r={params:d(d({},n.params),t.params),data:d(d({},n.data),t.data),resolve:d(d(d(d({},t.data),n.data),i?.data),t._resolvedData)}:r={params:d({},t.params),data:d({},t.data),resolve:d(d({},t.data),t._resolvedData??{})},i&&ri(i)&&(r.resolve[Ge]=i.title),r}var ne=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[Ge]}constructor(n,e,r,i,o,s,a,c,u){this.url=n,this.params=e,this.queryParams=r,this.fragment=i,this.data=o,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=u}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=ie(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=ie(this.queryParams),this._queryParamMap}toString(){let n=this.url.map(r=>r.toString()).join("/"),e=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${n}', path:'${e}')`}},Fe=class extends Mt{url;constructor(n,e){super(e),this.url=n,Tr(this,e)}toString(){return ti(this._root)}};function Tr(t,n){n.value._routerState=t,n.children.forEach(e=>Tr(t,e))}function ti(t){let n=t.children.length>0?` { ${t.children.map(ti).join(", ")} } `:"";return`${t.value}${n}`}function ur(t){if(t.snapshot){let n=t.snapshot,e=t._futureSnapshot;t.snapshot=e,$(n.queryParams,e.queryParams)||t.queryParamsSubject.next(e.queryParams),n.fragment!==e.fragment&&t.fragmentSubject.next(e.fragment),$(n.params,e.params)||t.paramsSubject.next(e.params),Fi(n.url,e.url)||t.urlSubject.next(e.url),$(n.data,e.data)||t.dataSubject.next(e.data)}else t.snapshot=t._futureSnapshot,t.dataSubject.next(t._futureSnapshot.data)}function yr(t,n){let e=$(t.params,n.params)&&Gi(t.url,n.url),r=!t.parent!=!n.parent;return e&&!r&&(!t.parent||yr(t.parent,n.parent))}function ri(t){return typeof t.title=="string"||t.title===null}var ni=new T(""),Ir=(()=>{class t{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=f;activateEvents=new te;deactivateEvents=new te;attachEvents=new te;detachEvents=new te;routerOutletData=Gr(void 0);parentContexts=p(ye);location=p(Xr);changeDetector=p(et);inputBinder=p(Nt,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(e){if(e.name){let{firstChange:r,previousValue:i}=e.name;if(r)return;this.isTrackedInParentContexts(i)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(i)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(e){return this.parentContexts.getContext(e)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let e=this.parentContexts.getContext(this.name);e?.route&&(e.attachRef?this.attach(e.attachRef,e.route):this.activateWith(e.route,e.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new w(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new w(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new w(4012,!1);this.location.detach();let e=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(e.instance),e}attach(e,r){this.activated=e,this._activatedRoute=r,this.location.insert(e.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(e.instance)}deactivate(){if(this.activated){let e=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(e)}}activateWith(e,r){if(this.isActivated)throw new w(4013,!1);this._activatedRoute=e;let i=this.location,s=e.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new Sr(e,a,i.injector,this.routerOutletData);this.activated=i.createComponent(s,{index:i.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||t)};static \u0275dir=Me({type:t,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[Ee]})}return t})(),Sr=class{route;childContexts;parent;outletData;constructor(n,e,r,i){this.route=n,this.childContexts=e,this.parent=r,this.outletData=i}get(n,e){return n===W?this.route:n===ye?this.childContexts:n===ni?this.outletData:this.parent.get(n,e)}},Nt=new T("");var Mr=(()=>{class t{static \u0275fac=function(r){return new(r||t)};static \u0275cmp=en({type:t,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,i){r&1&&an(0,"router-outlet")},dependencies:[Ir],encapsulation:2})}return t})();function Ar(t){let n=t.children&&t.children.map(Ar),e=n?M(d({},t),{children:n}):d({},t);return!e.component&&!e.loadComponent&&(n||e.loadChildren)&&e.outlet&&e.outlet!==f&&(e.component=Mr),e}function vo(t,n,e){let r=Be(t,n._root,e?e._root:void 0);return new He(r,n)}function Be(t,n,e){if(e&&t.shouldReuseRoute(n.value,e.value.snapshot)){let r=e.value;r._futureSnapshot=n.value;let i=mo(t,n,e);return new _(r,i)}else{if(t.shouldAttach(n.value)){let o=t.retrieve(n.value);if(o!==null){let s=o.route;return s.value._futureSnapshot=n.value,s.children=n.children.map(a=>Be(t,a)),s}}let r=yo(n.value),i=n.children.map(o=>Be(t,o));return new _(r,i)}}function mo(t,n,e){return n.children.map(r=>{for(let i of e.children)if(t.shouldReuseRoute(r.value,i.value.snapshot))return Be(t,r,i);return Be(t,r)})}function yo(t){return new W(new U(t.url),new U(t.params),new U(t.queryParams),new U(t.fragment),new U(t.data),t.outlet,t.component,t)}var ve=class{redirectTo;navigationBehaviorOptions;constructor(n,e){this.redirectTo=n,this.navigationBehaviorOptions=e}},ii="ngNavigationCancelingError";function Dt(t,n){let{redirectTo:e,navigationBehaviorOptions:r}=K(n)?{redirectTo:n,navigationBehaviorOptions:void 0}:n,i=oi(!1,A.Redirect);return i.url=e,i.navigationBehaviorOptions=r,i}function oi(t,n){let e=new Error(`NavigationCancelingError: ${t||""}`);return e[ii]=!0,e.cancellationCode=n,e}function So(t){return si(t)&&K(t.url)}function si(t){return!!t&&t[ii]}var Ro=(t,n,e,r)=>v(i=>(new Rr(n,i.targetRouterState,i.currentRouterState,e,r).activate(t),i)),Rr=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(n,e,r,i,o){this.routeReuseStrategy=n,this.futureState=e,this.currState=r,this.forwardEvent=i,this.inputBindingEnabled=o}activate(n){let e=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(e,r,n),ur(this.futureState.root),this.activateChildRoutes(e,r,n)}deactivateChildRoutes(n,e,r){let i=de(e);n.children.forEach(o=>{let s=o.value.outlet;this.deactivateRoutes(o,i[s],r),delete i[s]}),Object.values(i).forEach(o=>{this.deactivateRouteAndItsChildren(o,r)})}deactivateRoutes(n,e,r){let i=n.value,o=e?e.value:null;if(i===o)if(i.component){let s=r.getContext(i.outlet);s&&this.deactivateChildRoutes(n,e,s.children)}else this.deactivateChildRoutes(n,e,r);else o&&this.deactivateRouteAndItsChildren(e,r)}deactivateRouteAndItsChildren(n,e){n.value.component&&this.routeReuseStrategy.shouldDetach(n.value.snapshot)?this.detachAndStoreRouteSubtree(n,e):this.deactivateRouteAndOutlet(n,e)}detachAndStoreRouteSubtree(n,e){let r=e.getContext(n.value.outlet),i=r&&n.value.component?r.children:e,o=de(n);for(let s of Object.values(o))this.deactivateRouteAndItsChildren(s,i);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(n.value.snapshot,{componentRef:s,route:n,contexts:a})}}deactivateRouteAndOutlet(n,e){let r=e.getContext(n.value.outlet),i=r&&n.value.component?r.children:e,o=de(n);for(let s of Object.values(o))this.deactivateRouteAndItsChildren(s,i);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(n,e,r){let i=de(e);n.children.forEach(o=>{this.activateRoutes(o,i[o.value.outlet],r),this.forwardEvent(new Tt(o.value.snapshot))}),n.children.length&&this.forwardEvent(new Et(n.value.snapshot))}activateRoutes(n,e,r){let i=n.value,o=e?e.value:null;if(ur(i),i===o)if(i.component){let s=r.getOrCreateContext(i.outlet);this.activateChildRoutes(n,e,s.children)}else this.activateChildRoutes(n,e,r);else if(i.component){let s=r.getOrCreateContext(i.outlet);if(this.routeReuseStrategy.shouldAttach(i.snapshot)){let a=this.routeReuseStrategy.retrieve(i.snapshot);this.routeReuseStrategy.store(i.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),ur(a.route.value),this.activateChildRoutes(n,null,s.children)}else s.attachRef=null,s.route=i,s.outlet&&s.outlet.activateWith(i,s.injector),this.activateChildRoutes(n,null,s.children)}else this.activateChildRoutes(n,null,r)}},Ot=class{path;route;constructor(n){this.path=n,this.route=this.path[this.path.length-1]}},fe=class{component;route;constructor(n,e){this.component=n,this.route=e}};function wo(t,n,e){let r=t._root,i=n?n._root:null;return Pe(r,i,e,[r.value])}function Co(t){let n=t.routeConfig?t.routeConfig.canActivateChild:null;return!n||n.length===0?null:{node:t,guards:n}}function Se(t,n){let e=Symbol(),r=n.get(t,e);return r===e?typeof t=="function"&&!zr(t)?t:n.get(t):r}function Pe(t,n,e,r,i={canDeactivateChecks:[],canActivateChecks:[]}){let o=de(n);return t.children.forEach(s=>{Eo(s,o[s.value.outlet],e,r.concat([s.value]),i),delete o[s.value.outlet]}),Object.entries(o).forEach(([s,a])=>xe(a,e.getContext(s),i)),i}function Eo(t,n,e,r,i={canDeactivateChecks:[],canActivateChecks:[]}){let o=t.value,s=n?n.value:null,a=e?e.getContext(t.value.outlet):null;if(s&&o.routeConfig===s.routeConfig){let c=bo(s,o,o.routeConfig.runGuardsAndResolvers);c?i.canActivateChecks.push(new Ot(r)):(o.data=s.data,o._resolvedData=s._resolvedData),o.component?Pe(t,n,a?a.children:null,r,i):Pe(t,n,e,r,i),c&&a&&a.outlet&&a.outlet.isActivated&&i.canDeactivateChecks.push(new fe(a.outlet.component,s))}else s&&xe(n,a,i),i.canActivateChecks.push(new Ot(r)),o.component?Pe(t,null,a?a.children:null,r,i):Pe(t,null,e,r,i);return i}function bo(t,n,e){if(typeof e=="function")return e(t,n);switch(e){case"pathParamsChange":return!re(t.url,n.url);case"pathParamsOrQueryParamsChange":return!re(t.url,n.url)||!$(t.queryParams,n.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!yr(t,n)||!$(t.queryParams,n.queryParams);case"paramsChange":default:return!yr(t,n)}}function xe(t,n,e){let r=de(t),i=t.value;Object.entries(r).forEach(([o,s])=>{i.component?n?xe(s,n.children.getContext(o),e):xe(s,null,e):xe(s,n,e)}),i.component?n&&n.outlet&&n.outlet.isActivated?e.canDeactivateChecks.push(new fe(n.outlet.component,i)):e.canDeactivateChecks.push(new fe(null,i)):e.canDeactivateChecks.push(new fe(null,i))}function Ye(t){return typeof t=="function"}function To(t){return typeof t=="boolean"}function Io(t){return t&&Ye(t.canLoad)}function Mo(t){return t&&Ye(t.canActivate)}function Ao(t){return t&&Ye(t.canActivateChild)}function Do(t){return t&&Ye(t.canDeactivate)}function Oo(t){return t&&Ye(t.canMatch)}function ai(t){return t instanceof Pr||t?.name==="EmptyError"}var ut=Symbol("INITIAL_VALUE");function me(){return k(t=>$t(t.map(n=>n.pipe(ce(1),kr(ut)))).pipe(v(n=>{for(let e of n)if(e!==!0){if(e===ut)return ut;if(e===!1||_o(e))return e}return!0}),J(n=>n!==ut),ce(1)))}function _o(t){return K(t)||t instanceof ve}function No(t,n){return O(e=>{let{targetSnapshot:r,currentSnapshot:i,guards:{canActivateChecks:o,canDeactivateChecks:s}}=e;return s.length===0&&o.length===0?h(M(d({},e),{guardsResult:!0})):Uo(s,r,i,t).pipe(O(a=>a&&To(a)?Po(r,o,t,n):h(a)),v(a=>M(d({},e),{guardsResult:a})))})}function Uo(t,n,e,r){return D(t).pipe(O(i=>$o(i.component,i.route,e,n,r)),Y(i=>i!==!0,!0))}function Po(t,n,e,r){return D(n).pipe(ae(i=>Lr(xo(i.route.parent,r),Lo(i.route,r),ko(t,i.path,e),jo(t,i.route,e))),Y(i=>i!==!0,!0))}function Lo(t,n){return t!==null&&n&&n(new bt(t)),h(!0)}function xo(t,n){return t!==null&&n&&n(new Ct(t)),h(!0)}function jo(t,n,e){let r=n.routeConfig?n.routeConfig.canActivate:null;if(!r||r.length===0)return h(!0);let i=r.map(o=>Ht(()=>{let s=Ze(n)??e,a=Se(o,s),c=Mo(a)?a.canActivate(n,t):q(s,()=>a(n,t));return X(c).pipe(Y())}));return h(i).pipe(me())}function ko(t,n,e){let r=n[n.length-1],o=n.slice(0,n.length-1).reverse().map(s=>Co(s)).filter(s=>s!==null).map(s=>Ht(()=>{let a=s.guards.map(c=>{let u=Ze(s.node)??e,l=Se(c,u),m=Ao(l)?l.canActivateChild(r,t):q(u,()=>l(r,t));return X(m).pipe(Y())});return h(a).pipe(me())}));return h(o).pipe(me())}function $o(t,n,e,r,i){let o=n&&n.routeConfig?n.routeConfig.canDeactivate:null;if(!o||o.length===0)return h(!0);let s=o.map(a=>{let c=Ze(n)??i,u=Se(a,c),l=Do(u)?u.canDeactivate(t,n,e,r):q(c,()=>u(t,n,e,r));return X(l).pipe(Y())});return h(s).pipe(me())}function zo(t,n,e,r){let i=n.canLoad;if(i===void 0||i.length===0)return h(!0);let o=i.map(s=>{let a=Se(s,t),c=Io(a)?a.canLoad(n,e):q(t,()=>a(n,e));return X(c)});return h(o).pipe(me(),ci(r))}function ci(t){return Nr(b(n=>{if(typeof n!="boolean")throw Dt(t,n)}),v(n=>n===!0))}function Ho(t,n,e,r){let i=n.canMatch;if(!i||i.length===0)return h(!0);let o=i.map(s=>{let a=Se(s,t),c=Oo(a)?a.canMatch(n,e):q(t,()=>a(n,e));return X(c)});return h(o).pipe(me(),ci(r))}var Ve=class{segmentGroup;constructor(n){this.segmentGroup=n||null}},qe=class extends Error{urlTree;constructor(n){super(),this.urlTree=n}};function le(t){return we(new Ve(t))}function Fo(t){return we(new w(4e3,!1))}function Bo(t){return we(oi(!1,A.GuardRejected))}var wr=class{urlSerializer;urlTree;constructor(n,e){this.urlSerializer=n,this.urlTree=e}lineralizeSegments(n,e){let r=[],i=e.root;for(;;){if(r=r.concat(i.segments),i.numberOfChildren===0)return h(r);if(i.numberOfChildren>1||!i.children[f])return Fo(`${n.redirectTo}`);i=i.children[f]}}applyRedirectCommands(n,e,r,i,o){if(typeof e!="string"){let a=e,{queryParams:c,fragment:u,routeConfig:l,url:m,outlet:R,params:E,data:I,title:Z}=i,F=q(o,()=>a({params:E,data:I,queryParams:c,fragment:u,routeConfig:l,url:m,outlet:R,title:Z}));if(F instanceof H)throw new qe(F);e=F}let s=this.applyRedirectCreateUrlTree(e,this.urlSerializer.parse(e),n,r);if(e[0]==="/")throw new qe(s);return s}applyRedirectCreateUrlTree(n,e,r,i){let o=this.createSegmentGroup(n,e.root,r,i);return new H(o,this.createQueryParams(e.queryParams,this.urlTree.queryParams),e.fragment)}createQueryParams(n,e){let r={};return Object.entries(n).forEach(([i,o])=>{if(typeof o=="string"&&o[0]===":"){let a=o.substring(1);r[i]=e[a]}else r[i]=o}),r}createSegmentGroup(n,e,r,i){let o=this.createSegments(n,e.segments,r,i),s={};return Object.entries(e.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(n,c,r,i)}),new g(o,s)}createSegments(n,e,r,i){return e.map(o=>o.path[0]===":"?this.findPosParam(n,o,i):this.findOrReturn(o,r))}findPosParam(n,e,r){let i=r[e.path.substring(1)];if(!i)throw new w(4001,!1);return i}findOrReturn(n,e){let r=0;for(let i of e){if(i.path===n.path)return e.splice(r),i;r++}return n}},Cr={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function Vo(t,n,e,r,i){let o=ui(t,n,e);return o.matched?(r=fo(n,r),Ho(r,n,e,i).pipe(v(s=>s===!0?o:d({},Cr)))):h(o)}function ui(t,n,e){if(n.path==="**")return qo(e);if(n.path==="")return n.pathMatch==="full"&&(t.hasChildren()||e.length>0)?d({},Cr):{matched:!0,consumedSegments:[],remainingSegments:e,parameters:{},positionalParamSegments:{}};let i=(n.matcher||kn)(e,t,n);if(!i)return d({},Cr);let o={};Object.entries(i.posParams??{}).forEach(([a,c])=>{o[a]=c.path});let s=i.consumed.length>0?d(d({},o),i.consumed[i.consumed.length-1].parameters):o;return{matched:!0,consumedSegments:i.consumed,remainingSegments:e.slice(i.consumed.length),parameters:s,positionalParamSegments:i.posParams??{}}}function qo(t){return{matched:!0,parameters:t.length>0?zn(t).parameters:{},consumedSegments:t,remainingSegments:[],positionalParamSegments:{}}}function Ln(t,n,e,r){return e.length>0&&Zo(t,e,r)?{segmentGroup:new g(n,Wo(r,new g(e,t.children))),slicedSegments:[]}:e.length===0&&Yo(t,e,r)?{segmentGroup:new g(t.segments,Go(t,e,r,t.children)),slicedSegments:e}:{segmentGroup:new g(t.segments,t.children),slicedSegments:e}}function Go(t,n,e,r){let i={};for(let o of e)if(Ut(t,n,o)&&!r[x(o)]){let s=new g([],{});i[x(o)]=s}return d(d({},r),i)}function Wo(t,n){let e={};e[f]=n;for(let r of t)if(r.path===""&&x(r)!==f){let i=new g([],{});e[x(r)]=i}return e}function Zo(t,n,e){return e.some(r=>Ut(t,n,r)&&x(r)!==f)}function Yo(t,n,e){return e.some(r=>Ut(t,n,r))}function Ut(t,n,e){return(t.hasChildren()||n.length>0)&&e.pathMatch==="full"?!1:e.path===""}function Qo(t,n,e){return n.length===0&&!t.children[e]}var Er=class{};function Ko(t,n,e,r,i,o,s="emptyOnly"){return new br(t,n,e,r,i,s,o).recognize()}var Xo=31,br=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(n,e,r,i,o,s,a){this.injector=n,this.configLoader=e,this.rootComponentType=r,this.config=i,this.urlTree=o,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new wr(this.urlSerializer,this.urlTree)}noMatchError(n){return new w(4002,`'${n.segmentGroup}'`)}recognize(){let n=Ln(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(n).pipe(v(({children:e,rootSnapshot:r})=>{let i=new _(r,e),o=new Fe("",i),s=Yn(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,o.url=this.urlSerializer.serialize(s),{state:o,tree:s}}))}match(n){let e=new ne([],Object.freeze({}),Object.freeze(d({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),f,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,n,f,e).pipe(v(r=>({children:r,rootSnapshot:e})),ee(r=>{if(r instanceof qe)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof Ve?this.noMatchError(r):r}))}processSegmentGroup(n,e,r,i,o){return r.segments.length===0&&r.hasChildren()?this.processChildren(n,e,r,o):this.processSegment(n,e,r,r.segments,i,!0,o).pipe(v(s=>s instanceof _?[s]:[]))}processChildren(n,e,r,i){let o=[];for(let s of Object.keys(r.children))s==="primary"?o.unshift(s):o.push(s);return D(o).pipe(ae(s=>{let a=r.children[s],c=po(e,s);return this.processSegmentGroup(n,c,a,s,i)}),jr((s,a)=>(s.push(...a),s)),Ft(null),xr(),O(s=>{if(s===null)return le(r);let a=li(s);return Jo(a),h(a)}))}processSegment(n,e,r,i,o,s,a){return D(e).pipe(ae(c=>this.processSegmentAgainstRoute(c._injector??n,e,c,r,i,o,s,a).pipe(ee(u=>{if(u instanceof Ve)return h(null);throw u}))),Y(c=>!!c),ee(c=>{if(ai(c))return Qo(r,i,o)?h(new Er):le(r);throw c}))}processSegmentAgainstRoute(n,e,r,i,o,s,a,c){return x(r)!==s&&(s===f||!Ut(i,o,r))?le(i):r.redirectTo===void 0?this.matchSegmentAgainstRoute(n,i,r,o,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(n,i,e,r,o,s,c):le(i)}expandSegmentAgainstRouteUsingRedirect(n,e,r,i,o,s,a){let{matched:c,parameters:u,consumedSegments:l,positionalParamSegments:m,remainingSegments:R}=ui(e,i,o);if(!c)return le(e);typeof i.redirectTo=="string"&&i.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>Xo&&(this.allowRedirects=!1));let E=new ne(o,u,Object.freeze(d({},this.urlTree.queryParams)),this.urlTree.fragment,xn(i),x(i),i.component??i._loadedComponent??null,i,jn(i)),I=At(E,a,this.paramsInheritanceStrategy);E.params=Object.freeze(I.params),E.data=Object.freeze(I.data);let Z=this.applyRedirects.applyRedirectCommands(l,i.redirectTo,m,E,n);return this.applyRedirects.lineralizeSegments(i,Z).pipe(O(F=>this.processSegment(n,r,e,F.concat(R),s,!1,a)))}matchSegmentAgainstRoute(n,e,r,i,o,s){let a=Vo(e,r,i,n,this.urlSerializer);return r.path==="**"&&(e.children={}),a.pipe(k(c=>c.matched?(n=r._injector??n,this.getChildConfig(n,r,i).pipe(k(({routes:u})=>{let l=r._loadedInjector??n,{parameters:m,consumedSegments:R,remainingSegments:E}=c,I=new ne(R,m,Object.freeze(d({},this.urlTree.queryParams)),this.urlTree.fragment,xn(r),x(r),r.component??r._loadedComponent??null,r,jn(r)),Z=At(I,s,this.paramsInheritanceStrategy);I.params=Object.freeze(Z.params),I.data=Object.freeze(Z.data);let{segmentGroup:F,slicedSegments:xt}=Ln(e,R,E,u);if(xt.length===0&&F.hasChildren())return this.processChildren(l,u,F,I).pipe(v(Xe=>new _(I,Xe)));if(u.length===0&&xt.length===0)return h(new _(I,[]));let wi=x(r)===o;return this.processSegment(l,u,F,xt,wi?f:o,!0,I).pipe(v(Xe=>new _(I,Xe instanceof _?[Xe]:[])))}))):le(e)))}getChildConfig(n,e,r){return e.children?h({routes:e.children,injector:n}):e.loadChildren?e._loadedRoutes!==void 0?h({routes:e._loadedRoutes,injector:e._loadedInjector}):zo(n,e,r,this.urlSerializer).pipe(O(i=>i?this.configLoader.loadChildren(n,e).pipe(b(o=>{e._loadedRoutes=o.routes,e._loadedInjector=o.injector})):Bo(e))):h({routes:[],injector:n})}};function Jo(t){t.sort((n,e)=>n.value.outlet===f?-1:e.value.outlet===f?1:n.value.outlet.localeCompare(e.value.outlet))}function es(t){let n=t.value.routeConfig;return n&&n.path===""}function li(t){let n=[],e=new Set;for(let r of t){if(!es(r)){n.push(r);continue}let i=n.find(o=>r.value.routeConfig===o.value.routeConfig);i!==void 0?(i.children.push(...r.children),e.add(i)):n.push(r)}for(let r of e){let i=li(r.children);n.push(new _(r.value,i))}return n.filter(r=>!e.has(r))}function xn(t){return t.data||{}}function jn(t){return t.resolve||{}}function ts(t,n,e,r,i,o){return O(s=>Ko(t,n,e,r,s.extractedUrl,i,o).pipe(v(({state:a,tree:c})=>M(d({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function rs(t,n){return O(e=>{let{targetSnapshot:r,guards:{canActivateChecks:i}}=e;if(!i.length)return h(e);let o=new Set(i.map(c=>c.route)),s=new Set;for(let c of o)if(!s.has(c))for(let u of di(c))s.add(u);let a=0;return D(s).pipe(ae(c=>o.has(c)?ns(c,r,t,n):(c.data=At(c,c.parent,t).resolve,h(void 0))),b(()=>a++),Bt(1),O(c=>a===s.size?h(e):V))})}function di(t){let n=t.children.map(e=>di(e)).flat();return[t,...n]}function ns(t,n,e,r){let i=t.routeConfig,o=t._resolve;return i?.title!==void 0&&!ri(i)&&(o[Ge]=i.title),is(o,t,n,r).pipe(v(s=>(t._resolvedData=s,t.data=At(t,t.parent,e).resolve,null)))}function is(t,n,e,r){let i=hr(t);if(i.length===0)return h({});let o={};return D(i).pipe(O(s=>os(t[s],n,e,r).pipe(Y(),b(a=>{if(a instanceof ve)throw Dt(new oe,a);o[s]=a}))),Bt(1),v(()=>o),ee(s=>ai(s)?V:we(s)))}function os(t,n,e,r){let i=Ze(n)??r,o=Se(t,i),s=o.resolve?o.resolve(n,e):q(i,()=>o(n,e));return X(s)}function lr(t){return k(n=>{let e=t(n);return e?D(e).pipe(v(()=>n)):h(n)})}var Dr=(()=>{class t{buildTitle(e){let r,i=e.root;for(;i!==void 0;)r=this.getResolvedTitleForRoute(i)??r,i=i.children.find(o=>o.outlet===f);return r}getResolvedTitleForRoute(e){return e.data[Ge]}static \u0275fac=function(r){return new(r||t)};static \u0275prov=y({token:t,factory:()=>p(hi),providedIn:"root"})}return t})(),hi=(()=>{class t extends Dr{title;constructor(e){super(),this.title=e}updateTitle(e){let r=this.buildTitle(e);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||t)(S(On))};static \u0275prov=y({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),Qe=new T("",{providedIn:"root",factory:()=>({})}),Ke=new T(""),fi=(()=>{class t{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=p(hn);loadComponent(e){if(this.componentLoaders.get(e))return this.componentLoaders.get(e);if(e._loadedComponent)return h(e._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(e);let r=X(e.loadComponent()).pipe(v(gi),b(o=>{this.onLoadEndListener&&this.onLoadEndListener(e),e._loadedComponent=o}),Je(()=>{this.componentLoaders.delete(e)})),i=new kt(r,()=>new B).pipe(jt());return this.componentLoaders.set(e,i),i}loadChildren(e,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return h({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let o=pi(r,this.compiler,e,this.onLoadEndListener).pipe(Je(()=>{this.childrenLoaders.delete(r)})),s=new kt(o,()=>new B).pipe(jt());return this.childrenLoaders.set(r,s),s}static \u0275fac=function(r){return new(r||t)};static \u0275prov=y({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function pi(t,n,e,r){return X(t.loadChildren()).pipe(v(gi),O(i=>i instanceof Jr||Array.isArray(i)?h(i):D(n.compileModuleAsync(i))),v(i=>{r&&r(t);let o,s,a=!1;return Array.isArray(i)?(s=i,a=!0):(o=i.create(e).injector,s=o.get(Ke,[],{optional:!0,self:!0}).flat()),{routes:s.map(Ar),injector:o}}))}function ss(t){return t&&typeof t=="object"&&"default"in t}function gi(t){return ss(t)?t.default:t}var Pt=(()=>{class t{static \u0275fac=function(r){return new(r||t)};static \u0275prov=y({token:t,factory:()=>p(as),providedIn:"root"})}return t})(),as=(()=>{class t{shouldProcessUrl(e){return!0}extract(e){return e}merge(e,r){return e}static \u0275fac=function(r){return new(r||t)};static \u0275prov=y({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),vi=new T("");var mi=new T(""),yi=(()=>{class t{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new B;transitionAbortSubject=new B;configLoader=p(fi);environmentInjector=p(Ce);destroyRef=p(Vr);urlSerializer=p(We);rootContexts=p(ye);location=p(rt);inputBindingEnabled=p(Nt,{optional:!0})!==null;titleStrategy=p(Dr);options=p(Qe,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=p(Pt);createViewTransition=p(vi,{optional:!0});navigationErrorHandler=p(mi,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>h(void 0);rootComponentType=null;destroyed=!1;constructor(){let e=i=>this.events.next(new Rt(i)),r=i=>this.events.next(new wt(i));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=e,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(e){let r=++this.navigationId;this.transitions?.next(M(d({},e),{extractedUrl:this.urlHandlingStrategy.extract(e.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,id:r}))}setupNavigations(e){return this.transitions=new U(null),this.transitions.pipe(J(r=>r!==null),k(r=>{let i=!1,o=!1;return h(r).pipe(k(s=>{if(this.navigationId>r.id)return this.cancelNavigationTransition(r,"",A.SupersededByNewNavigation),V;this.currentTransition=r,this.currentNavigation={id:s.id,initialUrl:s.rawUrl,extractedUrl:s.extractedUrl,targetBrowserUrl:typeof s.extras.browserUrl=="string"?this.urlSerializer.parse(s.extras.browserUrl):s.extras.browserUrl,trigger:s.source,extras:s.extras,previousNavigation:this.lastSuccessfulNavigation?M(d({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let a=!e.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),c=s.extras.onSameUrlNavigation??e.onSameUrlNavigation;if(!a&&c!=="reload"){let u="";return this.events.next(new G(s.id,this.urlSerializer.serialize(s.rawUrl),u,ke.IgnoredSameUrlNavigation)),s.resolve(!1),V}if(this.urlHandlingStrategy.shouldProcessUrl(s.rawUrl))return h(s).pipe(k(u=>(this.events.next(new se(u.id,this.urlSerializer.serialize(u.extractedUrl),u.source,u.restoredState)),u.id!==this.navigationId?V:Promise.resolve(u))),ts(this.environmentInjector,this.configLoader,this.rootComponentType,e.config,this.urlSerializer,this.paramsInheritanceStrategy),b(u=>{r.targetSnapshot=u.targetSnapshot,r.urlAfterRedirects=u.urlAfterRedirects,this.currentNavigation=M(d({},this.currentNavigation),{finalUrl:u.urlAfterRedirects});let l=new $e(u.id,this.urlSerializer.serialize(u.extractedUrl),this.urlSerializer.serialize(u.urlAfterRedirects),u.targetSnapshot);this.events.next(l)}));if(a&&this.urlHandlingStrategy.shouldProcessUrl(s.currentRawUrl)){let{id:u,extractedUrl:l,source:m,restoredState:R,extras:E}=s,I=new se(u,this.urlSerializer.serialize(l),m,R);this.events.next(I);let Z=ei(this.rootComponentType).snapshot;return this.currentTransition=r=M(d({},s),{targetSnapshot:Z,urlAfterRedirects:l,extras:M(d({},E),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=l,h(r)}else{let u="";return this.events.next(new G(s.id,this.urlSerializer.serialize(s.extractedUrl),u,ke.IgnoredByUrlHandlingStrategy)),s.resolve(!1),V}}),b(s=>{let a=new vt(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}),v(s=>(this.currentTransition=r=M(d({},s),{guards:wo(s.targetSnapshot,s.currentSnapshot,this.rootContexts)}),r)),No(this.environmentInjector,s=>this.events.next(s)),b(s=>{if(r.guardsResult=s.guardsResult,s.guardsResult&&typeof s.guardsResult!="boolean")throw Dt(this.urlSerializer,s.guardsResult);let a=new mt(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot,!!s.guardsResult);this.events.next(a)}),J(s=>s.guardsResult?!0:(this.cancelNavigationTransition(s,"",A.GuardRejected),!1)),lr(s=>{if(s.guards.canActivateChecks.length!==0)return h(s).pipe(b(a=>{let c=new yt(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}),k(a=>{let c=!1;return h(a).pipe(rs(this.paramsInheritanceStrategy,this.environmentInjector),b({next:()=>c=!0,complete:()=>{c||this.cancelNavigationTransition(a,"",A.NoDataFromResolver)}}))}),b(a=>{let c=new St(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}))}),lr(s=>{let a=c=>{let u=[];c.routeConfig?.loadComponent&&!c.routeConfig._loadedComponent&&u.push(this.configLoader.loadComponent(c.routeConfig).pipe(b(l=>{c.component=l}),v(()=>{})));for(let l of c.children)u.push(...a(l));return u};return $t(a(s.targetSnapshot.root)).pipe(Ft(null),ce(1))}),lr(()=>this.afterPreactivation()),k(()=>{let{currentSnapshot:s,targetSnapshot:a}=r,c=this.createViewTransition?.(this.environmentInjector,s.root,a.root);return c?D(c).pipe(v(()=>r)):h(r)}),v(s=>{let a=vo(e.routeReuseStrategy,s.targetSnapshot,s.currentRouterState);return this.currentTransition=r=M(d({},s),{targetRouterState:a}),this.currentNavigation.targetRouterState=a,r}),b(()=>{this.events.next(new ze)}),Ro(this.rootContexts,e.routeReuseStrategy,s=>this.events.next(s),this.inputBindingEnabled),ce(1),b({next:s=>{i=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new j(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects))),this.titleStrategy?.updateTitle(s.targetRouterState.snapshot),s.resolve(!0)},complete:()=>{i=!0}}),$r(this.transitionAbortSubject.pipe(b(s=>{throw s}))),Je(()=>{!i&&!o&&this.cancelNavigationTransition(r,"",A.SupersededByNewNavigation),this.currentTransition?.id===r.id&&(this.currentNavigation=null,this.currentTransition=null)}),ee(s=>{if(this.destroyed)return r.resolve(!1),V;if(o=!0,si(s))this.events.next(new z(r.id,this.urlSerializer.serialize(r.extractedUrl),s.message,s.cancellationCode)),So(s)?this.events.next(new ge(s.url,s.navigationBehaviorOptions)):r.resolve(!1);else{let a=new pe(r.id,this.urlSerializer.serialize(r.extractedUrl),s,r.targetSnapshot??void 0);try{let c=q(this.environmentInjector,()=>this.navigationErrorHandler?.(a));if(c instanceof ve){let{message:u,cancellationCode:l}=Dt(this.urlSerializer,c);this.events.next(new z(r.id,this.urlSerializer.serialize(r.extractedUrl),u,l)),this.events.next(new ge(c.redirectTo,c.navigationBehaviorOptions))}else throw this.events.next(a),s}catch(c){this.options.resolveNavigationPromiseOnError?r.resolve(!1):r.reject(c)}}return V}))}))}cancelNavigationTransition(e,r,i){let o=new z(e.id,this.urlSerializer.serialize(e.extractedUrl),r,i);this.events.next(o),e.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let e=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return e.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||t)};static \u0275prov=y({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function cs(t){return t!==ft}var Si=(()=>{class t{static \u0275fac=function(r){return new(r||t)};static \u0275prov=y({token:t,factory:()=>p(us),providedIn:"root"})}return t})(),_t=class{shouldDetach(n){return!1}store(n,e){}shouldAttach(n){return!1}retrieve(n){return null}shouldReuseRoute(n,e){return n.routeConfig===e.routeConfig}},us=(()=>{class t extends _t{static \u0275fac=(()=>{let e;return function(i){return(e||(e=qt(t)))(i||t)}})();static \u0275prov=y({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),Ri=(()=>{class t{urlSerializer=p(We);options=p(Qe,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=p(rt);urlHandlingStrategy=p(Pt);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new H;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:e,initialUrl:r,targetBrowserUrl:i}){let o=e!==void 0?this.urlHandlingStrategy.merge(e,r):r,s=i??o;return s instanceof H?this.urlSerializer.serialize(s):s}commitTransition({targetRouterState:e,finalUrl:r,initialUrl:i}){r&&e?(this.currentUrlTree=r,this.rawUrlTree=this.urlHandlingStrategy.merge(r,i),this.routerState=e):this.rawUrlTree=i}routerState=ei(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:e}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,e??this.rawUrlTree)}static \u0275fac=function(r){return new(r||t)};static \u0275prov=y({token:t,factory:()=>p(ls),providedIn:"root"})}return t})(),ls=(()=>{class t extends Ri{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(e){return this.location.subscribe(r=>{r.type==="popstate"&&setTimeout(()=>{e(r.url,r.state,"popstate")})})}handleRouterEvent(e,r){e instanceof se?this.updateStateMemento():e instanceof G?this.commitTransition(r):e instanceof $e?this.urlUpdateStrategy==="eager"&&(r.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(r),r)):e instanceof ze?(this.commitTransition(r),this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(r),r)):e instanceof z&&(e.code===A.GuardRejected||e.code===A.NoDataFromResolver)?this.restoreHistory(r):e instanceof pe?this.restoreHistory(r,!0):e instanceof j&&(this.lastSuccessfulId=e.id,this.currentPageId=this.browserPageId)}setBrowserUrl(e,{extras:r,id:i}){let{replaceUrl:o,state:s}=r;if(this.location.isCurrentPathEqualTo(e)||o){let a=this.browserPageId,c=d(d({},s),this.generateNgRouterState(i,a));this.location.replaceState(e,"",c)}else{let a=d(d({},s),this.generateNgRouterState(i,this.browserPageId+1));this.location.go(e,"",a)}}restoreHistory(e,r=!1){if(this.canceledNavigationResolution==="computed"){let i=this.browserPageId,o=this.currentPageId-i;o!==0?this.location.historyGo(o):this.getCurrentUrlTree()===e.finalUrl&&o===0&&(this.resetInternalState(e),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetInternalState(e),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(e,r){return this.canceledNavigationResolution==="computed"?{navigationId:e,\u0275routerPageId:r}:{navigationId:e}}static \u0275fac=(()=>{let e;return function(i){return(e||(e=qt(t)))(i||t)}})();static \u0275prov=y({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function Or(t,n){t.events.pipe(J(e=>e instanceof j||e instanceof z||e instanceof pe||e instanceof G),v(e=>e instanceof j||e instanceof G?0:(e instanceof z?e.code===A.Redirect||e.code===A.SupersededByNewNavigation:!1)?2:1),J(e=>e!==2),ce(1)).subscribe(()=>{n()})}var ds={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},hs={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},Re=(()=>{class t{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=p(tn);stateManager=p(Ri);options=p(Qe,{optional:!0})||{};pendingTasks=p(qr);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=p(yi);urlSerializer=p(We);location=p(rt);urlHandlingStrategy=p(Pt);_events=new B;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=p(Si);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=p(Ke,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!p(Nt,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:e=>{this.console.warn(e)}}),this.subscribeToNavigationEvents()}eventsSubscription=new _r;subscribeToNavigationEvents(){let e=this.navigationTransitions.events.subscribe(r=>{try{let i=this.navigationTransitions.currentTransition,o=this.navigationTransitions.currentNavigation;if(i!==null&&o!==null){if(this.stateManager.handleRouterEvent(r,o),r instanceof z&&r.code!==A.Redirect&&r.code!==A.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof j)this.navigated=!0;else if(r instanceof ge){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,i.currentRawUrl),c=d({browserUrl:i.extras.browserUrl,info:i.extras.info,skipLocationChange:i.extras.skipLocationChange,replaceUrl:i.extras.replaceUrl||this.urlUpdateStrategy==="eager"||cs(i.source)},s);this.scheduleNavigation(a,ft,null,c,{resolve:i.resolve,reject:i.reject,promise:i.promise})}}ps(r)&&this._events.next(r)}catch(i){this.navigationTransitions.transitionAbortSubject.next(i)}});this.eventsSubscription.add(e)}resetRootComponentType(e){this.routerState.root.component=e,this.navigationTransitions.rootComponentType=e}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),ft,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((e,r,i)=>{this.navigateToSyncWithBrowser(e,i,r)})}navigateToSyncWithBrowser(e,r,i){let o={replaceUrl:!0},s=i?.navigationId?i:null;if(i){let c=d({},i);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(o.state=c)}let a=this.parseUrl(e);this.scheduleNavigation(a,r,s,o)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(e){this.config=e.map(Ar),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(e,r={}){let{relativeTo:i,queryParams:o,fragment:s,queryParamsHandling:a,preserveFragment:c}=r,u=c?this.currentUrlTree.fragment:s,l=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":l=d(d({},this.currentUrlTree.queryParams),o);break;case"preserve":l=this.currentUrlTree.queryParams;break;default:l=o||null}l!==null&&(l=this.removeEmptyProps(l));let m;try{let R=i?i.snapshot:this.routerState.snapshot.root;m=Qn(R)}catch{(typeof e[0]!="string"||e[0][0]!=="/")&&(e=[]),m=this.currentUrlTree.root}return Kn(m,e,l,u??null)}navigateByUrl(e,r={skipLocationChange:!1}){let i=K(e)?e:this.parseUrl(e),o=this.urlHandlingStrategy.merge(i,this.rawUrlTree);return this.scheduleNavigation(o,ft,null,r)}navigate(e,r={skipLocationChange:!1}){return fs(e),this.navigateByUrl(this.createUrlTree(e,r),r)}serializeUrl(e){return this.urlSerializer.serialize(e)}parseUrl(e){try{return this.urlSerializer.parse(e)}catch{return this.urlSerializer.parse("/")}}isActive(e,r){let i;if(r===!0?i=d({},ds):r===!1?i=d({},hs):i=r,K(e))return _n(this.currentUrlTree,e,i);let o=this.parseUrl(e);return _n(this.currentUrlTree,o,i)}removeEmptyProps(e){return Object.entries(e).reduce((r,[i,o])=>(o!=null&&(r[i]=o),r),{})}scheduleNavigation(e,r,i,o,s){if(this.disposed)return Promise.resolve(!1);let a,c,u;s?(a=s.resolve,c=s.reject,u=s.promise):u=new Promise((m,R)=>{a=m,c=R});let l=this.pendingTasks.add();return Or(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(l))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:i,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:e,extras:o,resolve:a,reject:c,promise:u,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),u.catch(m=>Promise.reject(m))}static \u0275fac=function(r){return new(r||t)};static \u0275prov=y({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function fs(t){for(let n=0;n<t.length;n++)if(t[n]==null)throw new w(4008,!1)}function ps(t){return!(t instanceof ze)&&!(t instanceof ge)}var Lt=(()=>{class t{router;route;tabIndexAttribute;renderer;el;locationStrategy;href=null;target;queryParams;fragment;queryParamsHandling;state;info;relativeTo;isAnchorElement;subscription;onChanges=new B;constructor(e,r,i,o,s,a){this.router=e,this.route=r,this.tabIndexAttribute=i,this.renderer=o,this.el=s,this.locationStrategy=a;let c=s.nativeElement.tagName?.toLowerCase();this.isAnchorElement=c==="a"||c==="area",this.isAnchorElement?this.subscription=e.events.subscribe(u=>{u instanceof j&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}preserveFragment=!1;skipLocationChange=!1;replaceUrl=!1;setTabIndexIfNotOnNativeEl(e){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",e)}ngOnChanges(e){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}routerLinkInput=null;set routerLink(e){e==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(K(e)?this.routerLinkInput=e:this.routerLinkInput=Array.isArray(e)?e:[e],this.setTabIndexIfNotOnNativeEl("0"))}onClick(e,r,i,o,s){let a=this.urlTree;if(a===null||this.isAnchorElement&&(e!==0||r||i||o||s||typeof this.target=="string"&&this.target!="_self"))return!0;let c={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(a,c),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let e=this.urlTree;this.href=e!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(e)):null;let r=this.href===null?null:Qr(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",r)}applyAttributeValue(e,r){let i=this.renderer,o=this.el.nativeElement;r!==null?i.setAttribute(o,e,r):i.removeAttribute(o,e)}get urlTree(){return this.routerLinkInput===null?null:K(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static \u0275fac=function(r){return new(r||t)(L(Re),L(W),Br("tabindex"),L(Kt),L(Zt),L(vn))};static \u0275dir=Me({type:t,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(r,i){r&1&&cn("click",function(s){return i.onClick(s.button,s.ctrlKey,s.shiftKey,s.altKey,s.metaKey)}),r&2&&sn("target",i.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",tt],skipLocationChange:[2,"skipLocationChange","skipLocationChange",tt],replaceUrl:[2,"replaceUrl","replaceUrl",tt],routerLink:"routerLink"},features:[Ee]})}return t})(),vs=(()=>{class t{router;element;renderer;cdr;link;links;classes=[];routerEventsSubscription;linkInputChangesSubscription;_isActive=!1;get isActive(){return this._isActive}routerLinkActiveOptions={exact:!1};ariaCurrentWhenActive;isActiveChange=new te;constructor(e,r,i,o,s){this.router=e,this.element=r,this.renderer=i,this.cdr=o,this.link=s,this.routerEventsSubscription=e.events.subscribe(a=>{a instanceof j&&this.update()})}ngAfterContentInit(){h(this.links.changes,h(null)).pipe(zt()).subscribe(e=>{this.update(),this.subscribeToEachLinkOnChanges()})}subscribeToEachLinkOnChanges(){this.linkInputChangesSubscription?.unsubscribe();let e=[...this.links.toArray(),this.link].filter(r=>!!r).map(r=>r.onChanges);this.linkInputChangesSubscription=D(e).pipe(zt()).subscribe(r=>{this._isActive!==this.isLinkActive(this.router)(r)&&this.update()})}set routerLinkActive(e){let r=Array.isArray(e)?e:e.split(" ");this.classes=r.filter(i=>!!i)}ngOnChanges(e){this.update()}ngOnDestroy(){this.routerEventsSubscription.unsubscribe(),this.linkInputChangesSubscription?.unsubscribe()}update(){!this.links||!this.router.navigated||queueMicrotask(()=>{let e=this.hasActiveLinks();this.classes.forEach(r=>{e?this.renderer.addClass(this.element.nativeElement,r):this.renderer.removeClass(this.element.nativeElement,r)}),e&&this.ariaCurrentWhenActive!==void 0?this.renderer.setAttribute(this.element.nativeElement,"aria-current",this.ariaCurrentWhenActive.toString()):this.renderer.removeAttribute(this.element.nativeElement,"aria-current"),this._isActive!==e&&(this._isActive=e,this.cdr.markForCheck(),this.isActiveChange.emit(e))})}isLinkActive(e){let r=ms(this.routerLinkActiveOptions)?this.routerLinkActiveOptions:this.routerLinkActiveOptions.exact||!1;return i=>{let o=i.urlTree;return o?e.isActive(o,r):!1}}hasActiveLinks(){let e=this.isLinkActive(this.router);return this.link&&e(this.link)||this.links.some(e)}static \u0275fac=function(r){return new(r||t)(L(Re),L(Zt),L(Kt),L(et),L(Lt,8))};static \u0275dir=Me({type:t,selectors:[["","routerLinkActive",""]],contentQueries:function(r,i,o){if(r&1&&un(o,Lt,5),r&2){let s;ln(s=dn())&&(i.links=s)}},inputs:{routerLinkActiveOptions:"routerLinkActiveOptions",ariaCurrentWhenActive:"ariaCurrentWhenActive",routerLinkActive:"routerLinkActive"},outputs:{isActiveChange:"isActiveChange"},exportAs:["routerLinkActive"],features:[Ee]})}return t})();function ms(t){return!!t.paths}var ys=new T("");function Ss(t,...n){return Hr([{provide:Ke,multi:!0,useValue:t},[],{provide:W,useFactory:Rs,deps:[Re]},{provide:nn,multi:!0,useFactory:ws},n.map(e=>e.\u0275providers)])}function Rs(t){return t.routerState.root}function ws(){let t=p(Gt);return n=>{let e=t.get(on);if(n!==e.components[0])return;let r=t.get(Re),i=t.get(Cs);t.get(Es)===1&&r.initialNavigation(),t.get(bs,null,Vt.Optional)?.setUpPreloading(),t.get(ys,null,Vt.Optional)?.init(),r.resetRootComponentType(e.componentTypes[0]),i.closed||(i.next(),i.complete(),i.unsubscribe())}}var Cs=new T("",{factory:()=>new B}),Es=new T("",{providedIn:"root",factory:()=>1});var bs=new T("");export{Pi as a,Ir as b,Lt as c,vs as d,Ss as e};
