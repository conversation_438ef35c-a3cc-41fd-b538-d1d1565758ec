/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    AUD: string[];
    BOB: string[];
    BYN: (string | undefined)[];
    COP: string[];
    JPY: string[];
    PHP: (string | undefined)[];
    PTE: string[];
    RON: (string | undefined)[];
    SCR: string[];
    SYP: (string | undefined)[];
    THB: string[];
    TWD: string[];
    USD: string[];
    VES: string[];
    XAF: string[];
    XOF: string[];
    XPF: string[];
    ZMW: (string | undefined)[];
} | undefined)[];
export default _default;
