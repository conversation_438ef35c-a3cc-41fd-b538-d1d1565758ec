/* src/styles.css */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
body {
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    Helvetica,
    Arial,
    sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f5f5f5;
}
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}
.card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin: 5px;
}
.btn:hover {
  background: #0056b3;
}
.btn-secondary {
  background: #6c757d;
}
.btn-secondary:hover {
  background: #545b62;
}
nav {
  background: #343a40;
  padding: 1rem 0;
}
nav ul {
  list-style: none;
  display: flex;
  justify-content: center;
}
nav li {
  margin: 0 15px;
}
nav a {
  color: white;
  text-decoration: none;
  padding: 10px 15px;
  border-radius: 4px;
  transition: background-color 0.3s;
}
nav a:hover,
nav a.active {
  background: #007bff;
}

/* angular:styles/global:styles */
/*# sourceMappingURL=styles.css.map */
