/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    BDT: (string | undefined)[];
    BWP: never[];
    BYN: (string | undefined)[];
    HKD: string[];
    JPY: string[];
    KRW: (string | undefined)[];
    PHP: (string | undefined)[];
    THB: string[];
    TWD: string[];
    USD: string[];
    XXX: never[];
    ZAR: never[];
    ZMW: never[];
})[];
export default _default;
