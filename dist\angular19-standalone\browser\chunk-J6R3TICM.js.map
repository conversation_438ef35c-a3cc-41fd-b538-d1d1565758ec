{"version": 3, "sources": ["src/app/components/home/<USER>"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterLink } from '@angular/router';\n\n@Component({\n  selector: 'app-home',\n  standalone: true,\n  imports: [CommonModule, RouterLink],\n  template: `\n    <div class=\"home\">\n      <div class=\"card\">\n        <h2>Welcome to Angular 19 Standalone Components!</h2>\n        <p>This is a demonstration of Angular 19's standalone components feature.</p>\n        \n        <div class=\"features\">\n          <h3>Features Demonstrated:</h3>\n          <ul>\n            <li>✅ Standalone Components (No NgModules required)</li>\n            <li>✅ Lazy Loading with Standalone Components</li>\n            <li>✅ Router Integration</li>\n            <li>✅ Signal-based State Management</li>\n            <li>✅ Modern Angular Architecture</li>\n          </ul>\n        </div>\n\n        <div class=\"quick-links\">\n          <h3>Quick Links:</h3>\n          <div class=\"link-buttons\">\n            <a routerLink=\"/counter\" class=\"btn\">Try Counter Example</a>\n            <a routerLink=\"/users\" class=\"btn\">View Users List</a>\n            <a routerLink=\"/about\" class=\"btn btn-secondary\">About This App</a>\n          </div>\n        </div>\n\n        <div class=\"info-box\">\n          <h4>What are Standalone Components?</h4>\n          <p>\n            Standalone components are a new feature in Angular that allows you to create components\n            without the need for NgModules. They can import their own dependencies directly,\n            making the application architecture simpler and more modular.\n          </p>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .home {\n      max-width: 800px;\n      margin: 0 auto;\n    }\n\n    .features ul {\n      list-style: none;\n      padding: 0;\n    }\n\n    .features li {\n      padding: 8px 0;\n      border-bottom: 1px solid #eee;\n    }\n\n    .quick-links {\n      margin: 30px 0;\n    }\n\n    .link-buttons {\n      display: flex;\n      gap: 10px;\n      flex-wrap: wrap;\n    }\n\n    .info-box {\n      background: #f8f9fa;\n      padding: 20px;\n      border-radius: 8px;\n      border-left: 4px solid #007bff;\n      margin-top: 30px;\n    }\n\n    .info-box h4 {\n      margin-top: 0;\n      color: #007bff;\n    }\n\n    h2 {\n      color: #1976d2;\n      margin-bottom: 20px;\n    }\n\n    h3 {\n      color: #333;\n      margin: 20px 0 10px 0;\n    }\n  `]\n})\nexport class HomeComponent {}\n"], "mappings": ";;;;;;;;;;;;;;;AA+FM,IAAO,gBAAP,MAAO,eAAa;;;uCAAb,gBAAa;IAAA;EAAA;;yEAAb,gBAAa,WAAA,CAAA,CAAA,UAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,MAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,GAAA,UAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,cAAA,YAAA,GAAA,KAAA,GAAA,CAAA,cAAA,UAAA,GAAA,KAAA,GAAA,CAAA,cAAA,UAAA,GAAA,OAAA,eAAA,GAAA,CAAA,GAAA,UAAA,CAAA,GAAA,UAAA,SAAA,uBAAA,IAAA,KAAA;AAAA,UAAA,KAAA,GAAA;AAtFtB,QAAA,yBAAA,GAAA,OAAA,CAAA,EAAkB,GAAA,OAAA,CAAA,EACE,GAAA,IAAA;AACZ,QAAA,iBAAA,GAAA,8CAAA;AAA4C,QAAA,uBAAA;AAChD,QAAA,yBAAA,GAAA,GAAA;AAAG,QAAA,iBAAA,GAAA,wEAAA;AAAsE,QAAA,uBAAA;AAEzE,QAAA,yBAAA,GAAA,OAAA,CAAA,EAAsB,GAAA,IAAA;AAChB,QAAA,iBAAA,GAAA,wBAAA;AAAsB,QAAA,uBAAA;AAC1B,QAAA,yBAAA,GAAA,IAAA,EAAI,IAAA,IAAA;AACE,QAAA,iBAAA,IAAA,sDAAA;AAA+C,QAAA,uBAAA;AACnD,QAAA,yBAAA,IAAA,IAAA;AAAI,QAAA,iBAAA,IAAA,gDAAA;AAAyC,QAAA,uBAAA;AAC7C,QAAA,yBAAA,IAAA,IAAA;AAAI,QAAA,iBAAA,IAAA,2BAAA;AAAoB,QAAA,uBAAA;AACxB,QAAA,yBAAA,IAAA,IAAA;AAAI,QAAA,iBAAA,IAAA,sCAAA;AAA+B,QAAA,uBAAA;AACnC,QAAA,yBAAA,IAAA,IAAA;AAAI,QAAA,iBAAA,IAAA,oCAAA;AAA6B,QAAA,uBAAA,EAAK,EACnC;AAGP,QAAA,yBAAA,IAAA,OAAA,CAAA,EAAyB,IAAA,IAAA;AACnB,QAAA,iBAAA,IAAA,cAAA;AAAY,QAAA,uBAAA;AAChB,QAAA,yBAAA,IAAA,OAAA,CAAA,EAA0B,IAAA,KAAA,CAAA;AACa,QAAA,iBAAA,IAAA,qBAAA;AAAmB,QAAA,uBAAA;AACxD,QAAA,yBAAA,IAAA,KAAA,CAAA;AAAmC,QAAA,iBAAA,IAAA,iBAAA;AAAe,QAAA,uBAAA;AAClD,QAAA,yBAAA,IAAA,KAAA,CAAA;AAAiD,QAAA,iBAAA,IAAA,gBAAA;AAAc,QAAA,uBAAA,EAAI,EAC/D;AAGR,QAAA,yBAAA,IAAA,OAAA,CAAA,EAAsB,IAAA,IAAA;AAChB,QAAA,iBAAA,IAAA,iCAAA;AAA+B,QAAA,uBAAA;AACnC,QAAA,yBAAA,IAAA,GAAA;AACE,QAAA,iBAAA,IAAA,0OAAA;AAGF,QAAA,uBAAA,EAAI,EACA,EACF;;sBAnCA,cAAc,UAAU,GAAA,QAAA,CAAA,u2BAAA,EAAA,CAAA;EAAA;;;sEAwFvB,eAAa,CAAA;UA3FzB;uBACW,YAAU,YACR,MAAI,SACP,CAAC,cAAc,UAAU,GAAC,UACzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAoCT,QAAA,CAAA,yyBAAA,EAAA,CAAA;;;;6EAmDU,eAAa,EAAA,WAAA,iBAAA,UAAA,6CAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}