import {
  RouterLink
} from "./chunk-ZA3JMKZC.js";
import {
  CommonModule,
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext
} from "./chunk-VPBA6LI2.js";

// src/app/components/home/<USER>
var HomeComponent = class _HomeComponent {
  static {
    this.\u0275fac = function HomeComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _HomeComponent)();
    };
  }
  static {
    this.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _HomeComponent, selectors: [["app-home"]], decls: 35, vars: 0, consts: [[1, "home"], [1, "card"], [1, "features"], [1, "quick-links"], [1, "link-buttons"], ["routerLink", "/counter", 1, "btn"], ["routerLink", "/users", 1, "btn"], ["routerLink", "/about", 1, "btn", "btn-secondary"], [1, "info-box"]], template: function HomeComponent_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "h2");
        \u0275\u0275text(3, "Welcome to Angular 19 Standalone Components!");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(4, "p");
        \u0275\u0275text(5, "This is a demonstration of Angular 19's standalone components feature.");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(6, "div", 2)(7, "h3");
        \u0275\u0275text(8, "Features Demonstrated:");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(9, "ul")(10, "li");
        \u0275\u0275text(11, "\u2705 Standalone Components (No NgModules required)");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(12, "li");
        \u0275\u0275text(13, "\u2705 Lazy Loading with Standalone Components");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(14, "li");
        \u0275\u0275text(15, "\u2705 Router Integration");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(16, "li");
        \u0275\u0275text(17, "\u2705 Signal-based State Management");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(18, "li");
        \u0275\u0275text(19, "\u2705 Modern Angular Architecture");
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(20, "div", 3)(21, "h3");
        \u0275\u0275text(22, "Quick Links:");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(23, "div", 4)(24, "a", 5);
        \u0275\u0275text(25, "Try Counter Example");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(26, "a", 6);
        \u0275\u0275text(27, "View Users List");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(28, "a", 7);
        \u0275\u0275text(29, "About This App");
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(30, "div", 8)(31, "h4");
        \u0275\u0275text(32, "What are Standalone Components?");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(33, "p");
        \u0275\u0275text(34, " Standalone components are a new feature in Angular that allows you to create components without the need for NgModules. They can import their own dependencies directly, making the application architecture simpler and more modular. ");
        \u0275\u0275elementEnd()()()();
      }
    }, dependencies: [CommonModule, RouterLink], styles: ["\n\n.home[_ngcontent-%COMP%] {\n  max-width: 800px;\n  margin: 0 auto;\n}\n.features[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\n  list-style: none;\n  padding: 0;\n}\n.features[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\n  padding: 8px 0;\n  border-bottom: 1px solid #eee;\n}\n.quick-links[_ngcontent-%COMP%] {\n  margin: 30px 0;\n}\n.link-buttons[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n}\n.info-box[_ngcontent-%COMP%] {\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 8px;\n  border-left: 4px solid #007bff;\n  margin-top: 30px;\n}\n.info-box[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  margin-top: 0;\n  color: #007bff;\n}\nh2[_ngcontent-%COMP%] {\n  color: #1976d2;\n  margin-bottom: 20px;\n}\nh3[_ngcontent-%COMP%] {\n  color: #333;\n  margin: 20px 0 10px 0;\n}\n/*# sourceMappingURL=home.component.css.map */"] });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(HomeComponent, [{
    type: Component,
    args: [{ selector: "app-home", standalone: true, imports: [CommonModule, RouterLink], template: `
    <div class="home">
      <div class="card">
        <h2>Welcome to Angular 19 Standalone Components!</h2>
        <p>This is a demonstration of Angular 19's standalone components feature.</p>
        
        <div class="features">
          <h3>Features Demonstrated:</h3>
          <ul>
            <li>\u2705 Standalone Components (No NgModules required)</li>
            <li>\u2705 Lazy Loading with Standalone Components</li>
            <li>\u2705 Router Integration</li>
            <li>\u2705 Signal-based State Management</li>
            <li>\u2705 Modern Angular Architecture</li>
          </ul>
        </div>

        <div class="quick-links">
          <h3>Quick Links:</h3>
          <div class="link-buttons">
            <a routerLink="/counter" class="btn">Try Counter Example</a>
            <a routerLink="/users" class="btn">View Users List</a>
            <a routerLink="/about" class="btn btn-secondary">About This App</a>
          </div>
        </div>

        <div class="info-box">
          <h4>What are Standalone Components?</h4>
          <p>
            Standalone components are a new feature in Angular that allows you to create components
            without the need for NgModules. They can import their own dependencies directly,
            making the application architecture simpler and more modular.
          </p>
        </div>
      </div>
    </div>
  `, styles: ["/* angular:styles/component:css;70e5e91bb23d49e8234a80b28b96e469859c32c7edd154dff1c3492c1e52f402;D:/learning/angular/standalone/src/app/components/home/<USER>/\n.home {\n  max-width: 800px;\n  margin: 0 auto;\n}\n.features ul {\n  list-style: none;\n  padding: 0;\n}\n.features li {\n  padding: 8px 0;\n  border-bottom: 1px solid #eee;\n}\n.quick-links {\n  margin: 30px 0;\n}\n.link-buttons {\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n}\n.info-box {\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 8px;\n  border-left: 4px solid #007bff;\n  margin-top: 30px;\n}\n.info-box h4 {\n  margin-top: 0;\n  color: #007bff;\n}\nh2 {\n  color: #1976d2;\n  margin-bottom: 20px;\n}\nh3 {\n  color: #333;\n  margin: 20px 0 10px 0;\n}\n/*# sourceMappingURL=home.component.css.map */\n"] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(HomeComponent, { className: "HomeComponent", filePath: "src/app/components/home/<USER>", lineNumber: 96 });
})();
export {
  HomeComponent
};
//# sourceMappingURL=chunk-J6R3TICM.js.map
