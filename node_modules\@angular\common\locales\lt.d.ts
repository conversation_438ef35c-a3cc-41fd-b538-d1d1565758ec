/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    AUD: (string | undefined)[];
    BDT: never[];
    BRL: (string | undefined)[];
    BYN: (string | undefined)[];
    CAD: (string | undefined)[];
    CNY: (string | undefined)[];
    GBP: (string | undefined)[];
    HKD: (string | undefined)[];
    ILS: never[];
    INR: never[];
    JPY: (string | undefined)[];
    KHR: never[];
    KRW: (string | undefined)[];
    LAK: never[];
    MNT: never[];
    MXN: (string | undefined)[];
    NZD: (string | undefined)[];
    PHP: (string | undefined)[];
    PLN: (string | undefined)[];
    PYG: (string | undefined)[];
    RUB: (string | undefined)[];
    TWD: (string | undefined)[];
    USD: (string | undefined)[];
    VND: never[];
    XAF: never[];
    XCD: (string | undefined)[];
    XOF: never[];
    XPF: never[];
} | undefined)[];
export default _default;
