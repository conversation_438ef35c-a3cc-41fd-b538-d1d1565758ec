{"version": 3, "sources": ["src/app/components/counter/counter.component.ts"], "sourcesContent": ["import { Component, signal, computed } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\n@Component({\n  selector: 'app-counter',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <div class=\"counter\">\n      <div class=\"card\">\n        <h2>Counter Example with Signals</h2>\n        <p>This demonstrates Angular's new signal-based reactivity system.</p>\n        \n        <div class=\"counter-display\">\n          <div class=\"count-value\">{{ count() }}</div>\n          <div class=\"count-info\">\n            <p>Double: {{ doubleCount() }}</p>\n            <p>Status: {{ countStatus() }}</p>\n          </div>\n        </div>\n\n        <div class=\"counter-controls\">\n          <button class=\"btn\" (click)=\"increment()\">\n            + Increment\n          </button>\n          <button class=\"btn\" (click)=\"decrement()\">\n            - Decrement\n          </button>\n          <button class=\"btn btn-secondary\" (click)=\"reset()\">\n            Reset\n          </button>\n        </div>\n\n        <div class=\"step-controls\">\n          <label for=\"step\">Step Size:</label>\n          <select id=\"step\" (change)=\"setStep($event)\" class=\"step-select\">\n            <option value=\"1\">1</option>\n            <option value=\"2\">2</option>\n            <option value=\"5\">5</option>\n            <option value=\"10\">10</option>\n          </select>\n        </div>\n\n        <div class=\"info-box\">\n          <h4>About Signals</h4>\n          <p>\n            Signals provide a new way to manage reactive state in Angular. They automatically\n            track dependencies and update the UI when values change, providing better performance\n            and developer experience.\n          </p>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .counter {\n      max-width: 600px;\n      margin: 0 auto;\n    }\n\n    .counter-display {\n      text-align: center;\n      margin: 30px 0;\n    }\n\n    .count-value {\n      font-size: 4rem;\n      font-weight: bold;\n      color: #1976d2;\n      margin-bottom: 20px;\n    }\n\n    .count-info {\n      display: flex;\n      justify-content: space-around;\n      margin: 20px 0;\n    }\n\n    .count-info p {\n      font-size: 1.2rem;\n      margin: 0;\n    }\n\n    .counter-controls {\n      display: flex;\n      justify-content: center;\n      gap: 10px;\n      margin: 30px 0;\n    }\n\n    .step-controls {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 10px;\n      margin: 20px 0;\n    }\n\n    .step-select {\n      padding: 8px;\n      border: 1px solid #ddd;\n      border-radius: 4px;\n      font-size: 14px;\n    }\n\n    .info-box {\n      background: #f8f9fa;\n      padding: 20px;\n      border-radius: 8px;\n      border-left: 4px solid #28a745;\n      margin-top: 30px;\n    }\n\n    .info-box h4 {\n      margin-top: 0;\n      color: #28a745;\n    }\n\n    h2 {\n      color: #1976d2;\n      text-align: center;\n      margin-bottom: 20px;\n    }\n  `]\n})\nexport class CounterComponent {\n  // Signal for the current count\n  count = signal(0);\n  \n  // Signal for the step size\n  step = signal(1);\n  \n  // Computed signal for double the count\n  doubleCount = computed(() => this.count() * 2);\n  \n  // Computed signal for count status\n  countStatus = computed(() => {\n    const value = this.count();\n    if (value === 0) return 'Zero';\n    if (value > 0) return 'Positive';\n    return 'Negative';\n  });\n\n  increment() {\n    this.count.update(value => value + this.step());\n  }\n\n  decrement() {\n    this.count.update(value => value - this.step());\n  }\n\n  reset() {\n    this.count.set(0);\n  }\n\n  setStep(event: Event) {\n    const target = event.target as HTMLSelectElement;\n    this.step.set(parseInt(target.value));\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AA6HM,IAAO,mBAAP,MAAO,kBAAgB;EA1H7B,cAAA;AA4HE,SAAA,QAAQ,OAAO,CAAC;AAGhB,SAAA,OAAO,OAAO,CAAC;AAGf,SAAA,cAAc,SAAS,MAAM,KAAK,MAAK,IAAK,CAAC;AAG7C,SAAA,cAAc,SAAS,MAAK;AAC1B,YAAM,QAAQ,KAAK,MAAK;AACxB,UAAI,UAAU;AAAG,eAAO;AACxB,UAAI,QAAQ;AAAG,eAAO;AACtB,aAAO;IACT,CAAC;;EAED,YAAS;AACP,SAAK,MAAM,OAAO,WAAS,QAAQ,KAAK,KAAI,CAAE;EAChD;EAEA,YAAS;AACP,SAAK,MAAM,OAAO,WAAS,QAAQ,KAAK,KAAI,CAAE;EAChD;EAEA,QAAK;AACH,SAAK,MAAM,IAAI,CAAC;EAClB;EAEA,QAAQ,OAAY;AAClB,UAAM,SAAS,MAAM;AACrB,SAAK,KAAK,IAAI,SAAS,OAAO,KAAK,CAAC;EACtC;;;uCAjCW,mBAAgB;IAAA;EAAA;;yEAAhB,mBAAgB,WAAA,CAAA,CAAA,aAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,OAAA,GAAA,OAAA,GAAA,CAAA,GAAA,OAAA,iBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,OAAA,MAAA,GAAA,CAAA,MAAA,QAAA,GAAA,eAAA,GAAA,QAAA,GAAA,CAAA,SAAA,GAAA,GAAA,CAAA,SAAA,GAAA,GAAA,CAAA,SAAA,GAAA,GAAA,CAAA,SAAA,IAAA,GAAA,CAAA,GAAA,UAAA,CAAA,GAAA,UAAA,SAAA,0BAAA,IAAA,KAAA;AAAA,UAAA,KAAA,GAAA;AArHzB,QAAA,yBAAA,GAAA,OAAA,CAAA,EAAqB,GAAA,OAAA,CAAA,EACD,GAAA,IAAA;AACZ,QAAA,iBAAA,GAAA,8BAAA;AAA4B,QAAA,uBAAA;AAChC,QAAA,yBAAA,GAAA,GAAA;AAAG,QAAA,iBAAA,GAAA,iEAAA;AAA+D,QAAA,uBAAA;AAElE,QAAA,yBAAA,GAAA,OAAA,CAAA,EAA6B,GAAA,OAAA,CAAA;AACF,QAAA,iBAAA,CAAA;AAAa,QAAA,uBAAA;AACtC,QAAA,yBAAA,GAAA,OAAA,CAAA,EAAwB,IAAA,GAAA;AACnB,QAAA,iBAAA,EAAA;AAA2B,QAAA,uBAAA;AAC9B,QAAA,yBAAA,IAAA,GAAA;AAAG,QAAA,iBAAA,EAAA;AAA2B,QAAA,uBAAA,EAAI,EAC9B;AAGR,QAAA,yBAAA,IAAA,OAAA,CAAA,EAA8B,IAAA,UAAA,CAAA;AACR,QAAA,qBAAA,SAAA,SAAA,qDAAA;AAAA,iBAAS,IAAA,UAAA;QAAW,CAAA;AACtC,QAAA,iBAAA,IAAA,eAAA;AACF,QAAA,uBAAA;AACA,QAAA,yBAAA,IAAA,UAAA,CAAA;AAAoB,QAAA,qBAAA,SAAA,SAAA,qDAAA;AAAA,iBAAS,IAAA,UAAA;QAAW,CAAA;AACtC,QAAA,iBAAA,IAAA,eAAA;AACF,QAAA,uBAAA;AACA,QAAA,yBAAA,IAAA,UAAA,CAAA;AAAkC,QAAA,qBAAA,SAAA,SAAA,qDAAA;AAAA,iBAAS,IAAA,MAAA;QAAO,CAAA;AAChD,QAAA,iBAAA,IAAA,SAAA;AACF,QAAA,uBAAA,EAAS;AAGX,QAAA,yBAAA,IAAA,OAAA,CAAA,EAA2B,IAAA,SAAA,CAAA;AACP,QAAA,iBAAA,IAAA,YAAA;AAAU,QAAA,uBAAA;AAC5B,QAAA,yBAAA,IAAA,UAAA,EAAA;AAAkB,QAAA,qBAAA,UAAA,SAAA,oDAAA,QAAA;AAAA,iBAAU,IAAA,QAAA,MAAA;QAAe,CAAA;AACzC,QAAA,yBAAA,IAAA,UAAA,EAAA;AAAkB,QAAA,iBAAA,IAAA,GAAA;AAAC,QAAA,uBAAA;AACnB,QAAA,yBAAA,IAAA,UAAA,EAAA;AAAkB,QAAA,iBAAA,IAAA,GAAA;AAAC,QAAA,uBAAA;AACnB,QAAA,yBAAA,IAAA,UAAA,EAAA;AAAkB,QAAA,iBAAA,IAAA,GAAA;AAAC,QAAA,uBAAA;AACnB,QAAA,yBAAA,IAAA,UAAA,EAAA;AAAmB,QAAA,iBAAA,IAAA,IAAA;AAAE,QAAA,uBAAA,EAAS,EACvB;AAGX,QAAA,yBAAA,IAAA,OAAA,EAAA,EAAsB,IAAA,IAAA;AAChB,QAAA,iBAAA,IAAA,eAAA;AAAa,QAAA,uBAAA;AACjB,QAAA,yBAAA,IAAA,GAAA;AACE,QAAA,iBAAA,IAAA,qMAAA;AAGF,QAAA,uBAAA,EAAI,EACA,EACF;;;AArCuB,QAAA,oBAAA,CAAA;AAAA,QAAA,4BAAA,IAAA,MAAA,CAAA;AAEpB,QAAA,oBAAA,CAAA;AAAA,QAAA,6BAAA,YAAA,IAAA,YAAA,GAAA,EAAA;AACA,QAAA,oBAAA,CAAA;AAAA,QAAA,6BAAA,YAAA,IAAA,YAAA,GAAA,EAAA;;sBAXH,YAAY,GAAA,QAAA,CAAA,4vCAAA,EAAA,CAAA;EAAA;;;sEAuHX,kBAAgB,CAAA;UA1H5B;uBACW,eAAa,YACX,MAAI,SACP,CAAC,YAAY,GAAC,UACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA8CT,QAAA,CAAA,mrCAAA,EAAA,CAAA;;;;6EAwEU,kBAAgB,EAAA,WAAA,oBAAA,UAAA,mDAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}