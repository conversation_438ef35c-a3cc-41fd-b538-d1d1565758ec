import { Component, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { UserCardComponent, User } from './user-card.component';
import { TaskFormComponent, Task } from './task-form.component';
import { ViewChildDemoComponent } from './viewchild-demo.component';

@Component({
  selector: 'app-decorators-examples',
  standalone: true,
  imports: [CommonModule, UserCardComponent, TaskFormComponent, ViewChildDemoComponent],
  template: `
    <div class="decorators-examples">
      <div class="container">
        <h1>Angular Decorators Examples</h1>
        <p>This page demonstrates &#64;Input, &#64;Output, and &#64;ViewChild decorators with practical examples.</p>

        <!-- Navigation Tabs -->
        <div class="tabs">
          <button 
            *ngFor="let tab of tabs" 
            class="tab-button"
            [class.active]="activeTab() === tab.id"
            (click)="setActiveTab(tab.id)"
          >
            {{ tab.label }}
          </button>
        </div>

        <!-- @Input Example -->
        <div *ngIf="activeTab() === 'input'" class="tab-content">
          <div class="card">
            <h2>&#64;Input Decorator Example</h2>
            <p>The &#64;Input decorator allows a parent component to pass data to a child component.</p>
            
            <div class="example-info">
              <h3>How it works:</h3>
              <ul>
                <li><strong>Parent Component:</strong> Passes data via property binding</li>
                <li><strong>Child Component:</strong> Receives data via &#64;Input decorator</li>
                <li><strong>Data Flow:</strong> One-way from parent to child</li>
              </ul>
            </div>

            <div class="controls">
              <h3>User Controls:</h3>
              <label>
                <input 
                  type="checkbox" 
                  [checked]="showActions()"
                  (change)="toggleActions()"
                >
                Show Action Buttons
              </label>
            </div>

            <div class="users-grid">
              <app-user-card 
                *ngFor="let user of sampleUsers" 
                [user]="user"
                [showActions]="showActions()"
                [defaultAvatar]="'https://via.placeholder.com/60x60/' + getRandomColor() + '/ffffff?text=' + user.name.charAt(0)"
              ></app-user-card>
            </div>

            <div class="code-example">
              <h4>Code Example:</h4>
              <pre><code>// Child Component (UserCardComponent)
&#64;Component({{ '{' }}
  selector: 'app-user-card',
  // ...
{{ '}' }})
export class UserCardComponent {{ '{' }}
  &#64;Input({{ '{' }} required: true {{ '}' }}) user!: User;
  &#64;Input() showActions: boolean = true;
  &#64;Input() defaultAvatar: string = '...';
{{ '}' }}

// Parent Component Template
&lt;app-user-card 
  [user]="userData"
  [showActions]="true"
  [defaultAvatar]="avatarUrl"
&gt;&lt;/app-user-card&gt;</code></pre>
            </div>
          </div>
        </div>

        <!-- @Output Example -->
        <div *ngIf="activeTab() === 'output'" class="tab-content">
          <div class="card">
            <h2>&#64;Output Decorator Example</h2>
            <p>The &#64;Output decorator allows a child component to emit events to its parent component.</p>
            
            <div class="example-info">
              <h3>How it works:</h3>
              <ul>
                <li><strong>Child Component:</strong> Emits events using EventEmitter</li>
                <li><strong>Parent Component:</strong> Listens to events via event binding</li>
                <li><strong>Data Flow:</strong> Events flow from child to parent</li>
              </ul>
            </div>

            <div class="tasks-section">
              <div class="tasks-list">
                <h3>Tasks List ({{ tasks().length }} tasks)</h3>
                <div *ngIf="tasks().length === 0" class="empty-state">
                  No tasks yet. Use the form below to add some!
                </div>
                <div *ngFor="let task of tasks()" class="task-item">
                  <div class="task-info">
                    <h4>{{ task.title }}</h4>
                    <p>{{ task.description }}</p>
                    <span class="priority-badge" [class]="'priority-' + task.priority">
                      {{ task.priority | titlecase }}
                    </span>
                    <span class="date">{{ task.createdAt | date:'short' }}</span>
                  </div>
                  <button class="btn btn-sm btn-danger" (click)="removeTask(task.id)">
                    Remove
                  </button>
                </div>
              </div>

              <app-task-form 
                (taskSubmitted)="onTaskAdded($event)"
                (formCancelled)="onFormCancelled()"
                (formReset)="onFormReset()"
              ></app-task-form>
            </div>

            <div class="events-log">
              <h3>Events Log:</h3>
              <div class="log-entries">
                <div *ngFor="let event of eventLog()" class="log-entry">
                  <span class="timestamp">{{ event.timestamp | date:'HH:mm:ss' }}</span>
                  <span class="event-name">{{ event.event }}</span>
                  <span class="event-data">{{ event.data }}</span>
                </div>
              </div>
              <button class="btn btn-secondary" (click)="clearEventLog()">Clear Log</button>
            </div>

            <div class="code-example">
              <h4>Code Example:</h4>
              <pre><code>// Child Component (TaskFormComponent)
&#64;Component({{ '{' }}
  selector: 'app-task-form',
  // ...
{{ '}' }})
export class TaskFormComponent {{ '{' }}
  &#64;Output() taskSubmitted = new EventEmitter&lt;Task&gt;();
  &#64;Output() formCancelled = new EventEmitter&lt;void&gt;();
  
  onSubmit() {{ '{' }}
    this.taskSubmitted.emit(newTask);
  {{ '}' }}
{{ '}' }}

// Parent Component Template
&lt;app-task-form 
  (taskSubmitted)="onTaskAdded($event)"
  (formCancelled)="onFormCancelled()"
&gt;&lt;/app-task-form&gt;</code></pre>
            </div>
          </div>
        </div>

        <!-- @ViewChild Example -->
        <div *ngIf="activeTab() === 'viewchild'" class="tab-content">
          <div class="card">
            <h2>&#64;ViewChild Decorator Example</h2>
            <p>The &#64;ViewChild decorator allows a parent component to access child component instances or DOM elements.</p>
            
            <div class="example-info">
              <h3>How it works:</h3>
              <ul>
                <li><strong>ElementRef:</strong> Access DOM elements directly</li>
                <li><strong>Component Reference:</strong> Access child component methods and properties</li>
                <li><strong>Template Reference:</strong> Access template reference variables</li>
                <li><strong>Lifecycle:</strong> Available after ngAfterViewInit</li>
              </ul>
            </div>

            <app-viewchild-demo></app-viewchild-demo>

            <div class="code-example">
              <h4>Code Example:</h4>
              <pre><code>// Component with ViewChild
export class ParentComponent implements AfterViewInit {{ '{' }}
  &#64;ViewChild('textInput') inputRef!: ElementRef&lt;HTMLInputElement&gt;;
  &#64;ViewChild('childComponent') childComp!: ChildComponent;
  &#64;ViewChild('canvas') canvasRef!: ElementRef&lt;HTMLCanvasElement&gt;;

  ngAfterViewInit() {{ '{' }}
    // ViewChild references are now available
    this.inputRef.nativeElement.focus();
    this.childComp.someMethod();
  {{ '}' }}
{{ '}' }}

// Template
&lt;input #textInput type="text"&gt;
&lt;app-child #childComponent&gt;&lt;/app-child&gt;
&lt;canvas #canvas&gt;&lt;/canvas&gt;</code></pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .decorators-examples {
      min-height: 100vh;
      background: #f5f5f5;
      padding: 20px 0;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }

    h1 {
      color: #1976d2;
      text-align: center;
      margin-bottom: 10px;
    }

    .tabs {
      display: flex;
      justify-content: center;
      margin: 30px 0;
      border-bottom: 2px solid #e9ecef;
    }

    .tab-button {
      padding: 12px 24px;
      border: none;
      background: none;
      cursor: pointer;
      font-size: 16px;
      font-weight: 500;
      color: #6c757d;
      border-bottom: 3px solid transparent;
      transition: all 0.3s ease;
    }

    .tab-button:hover {
      color: #007bff;
      background: #f8f9fa;
    }

    .tab-button.active {
      color: #007bff;
      border-bottom-color: #007bff;
    }

    .tab-content {
      margin-top: 30px;
    }

    .card {
      background: white;
      border-radius: 8px;
      padding: 30px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .example-info {
      background: #e3f2fd;
      padding: 20px;
      border-radius: 8px;
      margin: 20px 0;
      border-left: 4px solid #2196f3;
    }

    .example-info h3 {
      margin: 0 0 10px 0;
      color: #1976d2;
    }

    .example-info ul {
      margin: 0;
      padding-left: 20px;
    }

    .example-info li {
      margin: 8px 0;
    }

    .controls {
      margin: 20px 0;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .users-grid {
      display: grid;
      gap: 15px;
      margin: 20px 0;
    }

    .tasks-section {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 30px;
      margin: 20px 0;
    }

    .tasks-list {
      max-height: 400px;
      overflow-y: auto;
    }

    .empty-state {
      text-align: center;
      color: #6c757d;
      font-style: italic;
      padding: 20px;
    }

    .task-item {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 15px;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      margin: 10px 0;
      background: #f8f9fa;
    }

    .task-info h4 {
      margin: 0 0 5px 0;
      color: #333;
    }

    .task-info p {
      margin: 0 0 10px 0;
      color: #666;
      font-size: 14px;
    }

    .priority-badge {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
      margin-right: 10px;
    }

    .priority-low { background: #d4edda; color: #155724; }
    .priority-medium { background: #fff3cd; color: #856404; }
    .priority-high { background: #f8d7da; color: #721c24; }

    .date {
      font-size: 12px;
      color: #6c757d;
    }

    .events-log {
      margin: 30px 0;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .log-entries {
      max-height: 200px;
      overflow-y: auto;
      margin: 15px 0;
    }

    .log-entry {
      display: flex;
      gap: 15px;
      padding: 8px 0;
      border-bottom: 1px solid #e9ecef;
      font-size: 14px;
    }

    .timestamp {
      color: #6c757d;
      font-weight: bold;
      min-width: 80px;
    }

    .event-name {
      color: #007bff;
      font-weight: bold;
      min-width: 120px;
    }

    .code-example {
      margin: 30px 0;
      background: #f8f9fa;
      border-radius: 8px;
      padding: 20px;
      border-left: 4px solid #28a745;
    }

    .code-example h4 {
      margin: 0 0 15px 0;
      color: #28a745;
    }

    .code-example pre {
      background: #2d3748;
      color: #e2e8f0;
      padding: 15px;
      border-radius: 4px;
      overflow-x: auto;
      margin: 0;
      font-size: 14px;
      line-height: 1.5;
    }

    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.3s;
    }

    .btn-sm {
      padding: 6px 12px;
      font-size: 12px;
    }

    .btn-danger {
      background: #dc3545;
      color: white;
    }

    .btn-danger:hover {
      background: #c82333;
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background: #545b62;
    }

    @media (max-width: 768px) {
      .tabs {
        flex-direction: column;
      }
      
      .tasks-section {
        grid-template-columns: 1fr;
      }
      
      .task-item {
        flex-direction: column;
        gap: 10px;
      }
    }
  `]
})
export class DecoratorsExamplesComponent {
  // Tab management
  activeTab = signal<'input' | 'output' | 'viewchild'>('input');
  
  tabs = [
    { id: 'input' as const, label: '@Input Examples' },
    { id: 'output' as const, label: '@Output Examples' },
    { id: 'viewchild' as const, label: '@ViewChild Examples' }
  ];

  // @Input example data
  showActions = signal(true);
  sampleUsers: User[] = [
    { id: 1, name: 'John Doe', email: '<EMAIL>', role: 'Admin', isActive: true },
    { id: 2, name: 'Jane Smith', email: '<EMAIL>', role: 'User', isActive: true },
    { id: 3, name: 'Bob Johnson', email: '<EMAIL>', role: 'Manager', isActive: false }
  ];

  // @Output example data
  tasks = signal<Task[]>([]);
  eventLog = signal<Array<{timestamp: Date, event: string, data: string}>>([]);

  setActiveTab(tab: 'input' | 'output' | 'viewchild') {
    this.activeTab.set(tab);
  }

  // @Input example methods
  toggleActions() {
    this.showActions.update(value => !value);
  }

  getRandomColor(): string {
    const colors = ['007bff', '28a745', 'dc3545', 'ffc107', '17a2b8', '6f42c1'];
    return colors[Math.floor(Math.random() * colors.length)];
  }

  // @Output example methods
  onTaskAdded(task: Task) {
    this.tasks.update(tasks => [...tasks, task]);
    this.addEventLog('taskSubmitted', `Added task: "${task.title}"`);
  }

  onFormCancelled() {
    this.addEventLog('formCancelled', 'Form was cancelled');
  }

  onFormReset() {
    this.addEventLog('formReset', 'Form was reset');
  }

  removeTask(taskId: number) {
    this.tasks.update(tasks => tasks.filter(task => task.id !== taskId));
    this.addEventLog('taskRemoved', `Removed task with ID: ${taskId}`);
  }

  addEventLog(event: string, data: string) {
    this.eventLog.update(logs => [
      ...logs,
      { timestamp: new Date(), event, data }
    ].slice(-10)); // Keep only last 10 events
  }

  clearEventLog() {
    this.eventLog.set([]);
  }
}
