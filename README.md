# Angular 19 Standalone Components Example

This project demonstrates Angular 19's standalone components feature with practical examples and modern Angular patterns.

## 🚀 Features

- **Standalone Components**: No NgModules required
- **Signals**: New reactive state management system
- **Lazy Loading**: Route-based code splitting with standalone components
- **Modern Routing**: Simplified routing configuration
- **Responsive Design**: Mobile-first CSS design
- **TypeScript**: Full type safety
- **Zone.js Integration**: Proper change detection setup

## 📁 Project Structure

```
src/
├── app/
│   ├── components/
│   │   ├── home/           # Landing page component
│   │   ├── counter/        # Counter with signals demo
│   │   ├── users/          # User management with filtering
│   │   └── about/          # About page with project info
│   ├── app.component.ts    # Root component
│   └── app.routes.ts       # Route configuration
├── index.html              # Main HTML file
├── main.ts                 # Application bootstrap
└── styles.css              # Global styles
```

## 🛠️ Getting Started

### Prerequisites

- Node.js 18+ 
- npm 10+
- Angular CLI 19+

### Installation

1. Clone or download this project
2. Install dependencies:
   ```bash
   npm install
   ```

### Development

Start the development server:
```bash
npm start
# or
ng serve
```

Navigate to `http://localhost:4200/`. The application will automatically reload when you change any source files.

### Build

Build the project for production:
```bash
npm run build
# or
ng build
```

The build artifacts will be stored in the `dist/` directory.

## 📚 Components Overview

### Home Component
- Welcome page with project overview
- Navigation links to other components
- Information about standalone components

### Counter Component
- Demonstrates Angular Signals
- Interactive counter with step controls
- Computed signals for derived state
- Real-time reactivity examples

### Users Component
- User list management
- Search and filtering functionality
- Signal-based state management
- Dynamic CSS classes
- Form integration with FormsModule

### About Component
- Project information and documentation
- Technology stack overview
- Architecture explanation
- Feature highlights

## 🔧 Key Technologies

- **Angular 19**: Latest version with standalone components
- **TypeScript 5.6+**: Type-safe development
- **Signals**: Reactive state management
- **Router**: Lazy loading and navigation
- **CSS**: Modern responsive design

## 📖 Learning Resources

- [Angular Standalone Components Guide](https://angular.dev/guide/components/importing)
- [Angular Signals Documentation](https://angular.dev/guide/signals)
- [Angular Router Guide](https://angular.dev/guide/routing)

## 🎯 What You'll Learn

1. **Standalone Components**: How to create components without NgModules
2. **Signals**: Modern reactive state management
3. **Lazy Loading**: Code splitting with standalone components
4. **Modern Routing**: Functional route configuration
5. **Component Communication**: Signal-based data flow
6. **Performance**: Optimized bundle size and runtime performance

## 🚀 Next Steps

- Add more complex examples
- Implement HTTP client integration
- Add unit tests with Jasmine/Karma
- Explore Angular Material integration
- Add PWA features

## 📄 License

This project is for educational purposes and is free to use and modify.
