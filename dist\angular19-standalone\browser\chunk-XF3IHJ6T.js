var Ju=Object.defineProperty,Xu=Object.defineProperties;var el=Object.getOwnPropertyDescriptors;var Gi=Object.getOwnPropertySymbols;var tl=Object.prototype.hasOwnProperty,nl=Object.prototype.propertyIsEnumerable;var Wi=(e,t,n)=>t in e?Ju(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,J=(e,t)=>{for(var n in t||={})tl.call(t,n)&&Wi(e,n,t[n]);if(Gi)for(var n of Gi(t))nl.call(t,n)&&Wi(e,n,t[n]);return e},X=(e,t)=>Xu(e,el(t));function wr(e,t){return Object.is(e,t)}var F=null,Gt=!1,Cr=1,Y=Symbol("SIGNAL");function E(e){let t=F;return F=e,t}function br(){return F}var pt={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function ht(e){if(Gt)throw new Error("");if(F===null)return;F.consumerOnSignalRead(e);let t=F.nextProducerIndex++;if(Yt(F),t<F.producerNode.length&&F.producerNode[t]!==e&&ft(F)){let n=F.producerNode[t];Zt(n,F.producerIndexOfThis[t])}F.producerNode[t]!==e&&(F.producerNode[t]=e,F.producerIndexOfThis[t]=ft(F)?Zi(e,F,t):0),F.producerLastReadVersion[t]=e.version}function qi(){Cr++}function _r(e){if(!(ft(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Cr)){if(!e.producerMustRecompute(e)&&!Nr(e)){Ir(e);return}e.producerRecomputeValue(e),Ir(e)}}function Mr(e){if(e.liveConsumerNode===void 0)return;let t=Gt;Gt=!0;try{for(let n of e.liveConsumerNode)n.dirty||rl(n)}finally{Gt=t}}function Tr(){return F?.consumerAllowSignalWrites!==!1}function rl(e){e.dirty=!0,Mr(e),e.consumerMarkedDirty?.(e)}function Ir(e){e.dirty=!1,e.lastCleanEpoch=Cr}function qt(e){return e&&(e.nextProducerIndex=0),E(e)}function Sr(e,t){if(E(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(ft(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)Zt(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function Nr(e){Yt(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(_r(n),r!==n.version))return!0}return!1}function xr(e){if(Yt(e),ft(e))for(let t=0;t<e.producerNode.length;t++)Zt(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function Zi(e,t,n){if(Yi(e),e.liveConsumerNode.length===0&&Qi(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=Zi(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function Zt(e,t){if(Yi(e),e.liveConsumerNode.length===1&&Qi(e))for(let r=0;r<e.producerNode.length;r++)Zt(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];Yt(o),o.producerIndexOfThis[r]=t}}function ft(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function Yt(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function Yi(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function Qi(e){return e.producerNode!==void 0}function Ar(e,t){let n=Object.create(ol);n.computation=e,t!==void 0&&(n.equal=t);let r=()=>{if(_r(n),ht(n),n.value===Wt)throw n.error;return n.value};return r[Y]=n,r}var vr=Symbol("UNSET"),Er=Symbol("COMPUTING"),Wt=Symbol("ERRORED"),ol=X(J({},pt),{value:vr,dirty:!0,error:null,equal:wr,kind:"computed",producerMustRecompute(e){return e.value===vr||e.value===Er},producerRecomputeValue(e){if(e.value===Er)throw new Error("Detected cycle in computations.");let t=e.value;e.value=Er;let n=qt(e),r,o=!1;try{r=e.computation(),E(null),o=t!==vr&&t!==Wt&&r!==Wt&&e.equal(t,r)}catch(i){r=Wt,e.error=i}finally{Sr(e,n)}if(o){e.value=t;return}e.value=r,e.version++}});function il(){throw new Error}var Ki=il;function Ji(e){Ki(e)}function Rr(e){Ki=e}var sl=null;function Or(e,t){let n=Object.create(Qt);n.value=e,t!==void 0&&(n.equal=t);let r=()=>(ht(n),n.value);return r[Y]=n,r}function gt(e,t){Tr()||Ji(e),e.equal(e.value,t)||(e.value=t,al(e))}function kr(e,t){Tr()||Ji(e),gt(e,t(e.value))}var Qt=X(J({},pt),{equal:wr,value:void 0,kind:"signal"});function al(e){e.version++,qi(),Mr(e),sl?.()}function Fr(e){let t=E(null);try{return e()}finally{E(t)}}var Pr;function mt(){return Pr}function pe(e){let t=Pr;return Pr=e,t}var Kt=Symbol("NotFound");function g(e){return typeof e=="function"}function ze(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var Jt=ze(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function yt(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var L=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(g(r))try{r()}catch(i){t=i instanceof Jt?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{Xi(i)}catch(s){t=t??[],s instanceof Jt?t=[...t,...s.errors]:t.push(s)}}if(t)throw new Jt(t)}}add(t){var n;if(t&&t!==this)if(this.closed)Xi(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&yt(n,t)}remove(t){let{_finalizers:n}=this;n&&yt(n,t),t instanceof e&&t._removeParent(this)}};L.EMPTY=(()=>{let e=new L;return e.closed=!0,e})();var Lr=L.EMPTY;function Xt(e){return e instanceof L||e&&"closed"in e&&g(e.remove)&&g(e.add)&&g(e.unsubscribe)}function Xi(e){g(e)?e():e.unsubscribe()}var ee={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Ge={setTimeout(e,t,...n){let{delegate:r}=Ge;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=Ge;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function en(e){Ge.setTimeout(()=>{let{onUnhandledError:t}=ee;if(t)t(e);else throw e})}function Dt(){}var es=jr("C",void 0,void 0);function ts(e){return jr("E",void 0,e)}function ns(e){return jr("N",e,void 0)}function jr(e,t,n){return{kind:e,value:t,error:n}}var Te=null;function We(e){if(ee.useDeprecatedSynchronousErrorHandling){let t=!Te;if(t&&(Te={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=Te;if(Te=null,n)throw r}}else e()}function rs(e){ee.useDeprecatedSynchronousErrorHandling&&Te&&(Te.errorThrown=!0,Te.error=e)}var Se=class extends L{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,Xt(t)&&t.add(this)):this.destination=pl}static create(t,n,r){return new qe(t,n,r)}next(t){this.isStopped?Br(ns(t),this):this._next(t)}error(t){this.isStopped?Br(ts(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Br(es,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},dl=Function.prototype.bind;function Vr(e,t){return dl.call(e,t)}var Hr=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){tn(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){tn(r)}else tn(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){tn(n)}}},qe=class extends Se{constructor(t,n,r){super();let o;if(g(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&ee.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&Vr(t.next,i),error:t.error&&Vr(t.error,i),complete:t.complete&&Vr(t.complete,i)}):o=t}this.destination=new Hr(o)}};function tn(e){ee.useDeprecatedSynchronousErrorHandling?rs(e):en(e)}function fl(e){throw e}function Br(e,t){let{onStoppedNotification:n}=ee;n&&Ge.setTimeout(()=>n(e,t))}var pl={closed:!0,next:Dt,error:fl,complete:Dt};var Ze=typeof Symbol=="function"&&Symbol.observable||"@@observable";function q(e){return e}function hl(...e){return $r(e)}function $r(e){return e.length===0?q:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var M=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=ml(n)?n:new qe(n,r,o);return We(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=os(r),new r((o,i)=>{let s=new qe({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[Ze](){return this}pipe(...n){return $r(n)(this)}toPromise(n){return n=os(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function os(e){var t;return(t=e??ee.Promise)!==null&&t!==void 0?t:Promise}function gl(e){return e&&g(e.next)&&g(e.error)&&g(e.complete)}function ml(e){return e&&e instanceof Se||gl(e)&&Xt(e)}function Ur(e){return g(e?.lift)}function C(e){return t=>{if(Ur(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function I(e,t,n,r,o){return new zr(e,t,n,r,o)}var zr=class extends Se{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function Gr(){return C((e,t)=>{let n=null;e._refCount++;let r=I(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var Wr=class extends M{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,Ur(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new L;let n=this.getSubject();t.add(this.source.subscribe(I(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=L.EMPTY)}return t}refCount(){return Gr()(this)}};var is=ze(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var se=(()=>{class e extends M{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new nn(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new is}next(n){We(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){We(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){We(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Lr:(this.currentObservers=null,i.push(n),new L(()=>{this.currentObservers=null,yt(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new M;return n.source=this,n}}return e.create=(t,n)=>new nn(t,n),e})(),nn=class extends se{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:Lr}};var vt=class extends se{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var Et=new M(e=>e.complete());function ss(e){return e&&g(e.schedule)}function as(e){return e[e.length-1]}function rn(e){return g(as(e))?e.pop():void 0}function Ee(e){return ss(as(e))?e.pop():void 0}function us(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(l){try{u(r.next(l))}catch(f){s(f)}}function c(l){try{u(r.throw(l))}catch(f){s(f)}}function u(l){l.done?i(l.value):o(l.value).then(a,c)}u((r=r.apply(e,t||[])).next())})}function cs(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function Ne(e){return this instanceof Ne?(this.v=e,this):new Ne(e)}function ls(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(d){return function(h){return Promise.resolve(h).then(d,f)}}function a(d,h){r[d]&&(o[d]=function(w){return new Promise(function(P,A){i.push([d,w,P,A])>1||c(d,w)})},h&&(o[d]=h(o[d])))}function c(d,h){try{u(r[d](h))}catch(w){p(i[0][3],w)}}function u(d){d.value instanceof Ne?Promise.resolve(d.value.v).then(l,f):p(i[0][2],d)}function l(d){c("next",d)}function f(d){c("throw",d)}function p(d,h){d(h),i.shift(),i.length&&c(i[0][0],i[0][1])}}function ds(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof cs=="function"?cs(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(u){i({value:u,done:a})},s)}}var on=e=>e&&typeof e.length=="number"&&typeof e!="function";function sn(e){return g(e?.then)}function an(e){return g(e[Ze])}function cn(e){return Symbol.asyncIterator&&g(e?.[Symbol.asyncIterator])}function un(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function yl(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var ln=yl();function dn(e){return g(e?.[ln])}function fn(e){return ls(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield Ne(n.read());if(o)return yield Ne(void 0);yield yield Ne(r)}}finally{n.releaseLock()}})}function pn(e){return g(e?.getReader)}function O(e){if(e instanceof M)return e;if(e!=null){if(an(e))return Dl(e);if(on(e))return vl(e);if(sn(e))return El(e);if(cn(e))return fs(e);if(dn(e))return Il(e);if(pn(e))return wl(e)}throw un(e)}function Dl(e){return new M(t=>{let n=e[Ze]();if(g(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function vl(e){return new M(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function El(e){return new M(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,en)})}function Il(e){return new M(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function fs(e){return new M(t=>{Cl(e,t).catch(n=>t.error(n))})}function wl(e){return fs(fn(e))}function Cl(e,t){var n,r,o,i;return us(this,void 0,void 0,function*(){try{for(n=ds(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function $(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function hn(e,t=0){return C((n,r)=>{n.subscribe(I(r,o=>$(r,e,()=>r.next(o),t),()=>$(r,e,()=>r.complete(),t),o=>$(r,e,()=>r.error(o),t)))})}function gn(e,t=0){return C((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function ps(e,t){return O(e).pipe(gn(t),hn(t))}function hs(e,t){return O(e).pipe(gn(t),hn(t))}function gs(e,t){return new M(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function ms(e,t){return new M(n=>{let r;return $(n,t,()=>{r=e[ln](),$(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>g(r?.return)&&r.return()})}function mn(e,t){if(!e)throw new Error("Iterable cannot be null");return new M(n=>{$(n,t,()=>{let r=e[Symbol.asyncIterator]();$(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function ys(e,t){return mn(fn(e),t)}function Ds(e,t){if(e!=null){if(an(e))return ps(e,t);if(on(e))return gs(e,t);if(sn(e))return hs(e,t);if(cn(e))return mn(e,t);if(dn(e))return ms(e,t);if(pn(e))return ys(e,t)}throw un(e)}function Ie(e,t){return t?Ds(e,t):O(e)}function bl(...e){let t=Ee(e);return Ie(e,t)}function _l(e,t){let n=g(e)?e:()=>e,r=o=>o.error(n());return new M(t?o=>t.schedule(r,0,o):r)}function Ml(e){return!!e&&(e instanceof M||g(e.lift)&&g(e.subscribe))}var xe=ze(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function Ae(e,t){return C((n,r)=>{let o=0;n.subscribe(I(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:Tl}=Array;function Sl(e,t){return Tl(t)?e(...t):e(t)}function yn(e){return Ae(t=>Sl(e,t))}var{isArray:Nl}=Array,{getPrototypeOf:xl,prototype:Al,keys:Rl}=Object;function Dn(e){if(e.length===1){let t=e[0];if(Nl(t))return{args:t,keys:null};if(Ol(t)){let n=Rl(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function Ol(e){return e&&typeof e=="object"&&xl(e)===Al}function vn(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function kl(...e){let t=Ee(e),n=rn(e),{args:r,keys:o}=Dn(e);if(r.length===0)return Ie([],t);let i=new M(Fl(r,t,o?s=>vn(o,s):q));return n?i.pipe(yn(n)):i}function Fl(e,t,n=q){return r=>{vs(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)vs(t,()=>{let u=Ie(e[c],t),l=!1;u.subscribe(I(r,f=>{i[c]=f,l||(l=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function vs(e,t,n){e?$(n,e,t):t()}function Es(e,t,n,r,o,i,s,a){let c=[],u=0,l=0,f=!1,p=()=>{f&&!c.length&&!u&&t.complete()},d=w=>u<r?h(w):c.push(w),h=w=>{i&&t.next(w),u++;let P=!1;O(n(w,l++)).subscribe(I(t,A=>{o?.(A),i?d(A):t.next(A)},()=>{P=!0},void 0,()=>{if(P)try{for(u--;c.length&&u<r;){let A=c.shift();s?$(t,s,()=>h(A)):h(A)}p()}catch(A){t.error(A)}}))};return e.subscribe(I(t,d,()=>{f=!0,p()})),()=>{a?.()}}function Re(e,t,n=1/0){return g(t)?Re((r,o)=>Ae((i,s)=>t(r,i,o,s))(O(e(r,o))),n):(typeof t=="number"&&(n=t),C((r,o)=>Es(r,o,e,n)))}function qr(e=1/0){return Re(q,e)}function Is(){return qr(1)}function En(...e){return Is()(Ie(e,Ee(e)))}function Pl(e){return new M(t=>{O(e()).subscribe(t)})}function Ll(...e){let t=rn(e),{args:n,keys:r}=Dn(e),o=new M(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),c=s,u=s;for(let l=0;l<s;l++){let f=!1;O(n[l]).subscribe(I(i,p=>{f||(f=!0,u--),a[l]=p},()=>c--,void 0,()=>{(!c||!f)&&(u||i.next(r?vn(r,a):a),i.complete())}))}});return t?o.pipe(yn(t)):o}function It(e,t){return C((n,r)=>{let o=0;n.subscribe(I(r,i=>e.call(t,i,o++)&&r.next(i)))})}function ws(e){return C((t,n)=>{let r=null,o=!1,i;r=t.subscribe(I(n,void 0,void 0,s=>{i=O(e(s,ws(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function Cs(e,t,n,r,o){return(i,s)=>{let a=n,c=t,u=0;i.subscribe(I(s,l=>{let f=u++;c=a?e(c,l,f):(a=!0,l),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function jl(e,t){return g(t)?Re(e,t,1):Re(e,1)}function wt(e){return C((t,n)=>{let r=!1;t.subscribe(I(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function Zr(e){return e<=0?()=>Et:C((t,n)=>{let r=0;t.subscribe(I(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function In(e=Vl){return C((t,n)=>{let r=!1;t.subscribe(I(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function Vl(){return new xe}function Bl(e){return C((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function Hl(e,t){let n=arguments.length>=2;return r=>r.pipe(e?It((o,i)=>e(o,i,r)):q,Zr(1),n?wt(t):In(()=>new xe))}function Yr(e){return e<=0?()=>Et:C((t,n)=>{let r=[];t.subscribe(I(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function $l(e,t){let n=arguments.length>=2;return r=>r.pipe(e?It((o,i)=>e(o,i,r)):q,Yr(1),n?wt(t):In(()=>new xe))}function Ul(e,t){return C(Cs(e,t,arguments.length>=2,!0))}function zl(...e){let t=Ee(e);return C((n,r)=>{(t?En(e,n,t):En(e,n)).subscribe(r)})}function Gl(e,t){return C((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(I(r,c=>{o?.unsubscribe();let u=0,l=i++;O(e(c,l)).subscribe(o=I(r,f=>r.next(t?t(c,f,l,u++):f),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function Wl(e){return C((t,n)=>{O(e).subscribe(I(n,()=>n.complete(),Dt)),!n.closed&&t.subscribe(n)})}function ql(e,t,n){let r=g(e)||t||n?{next:e,error:t,complete:n}:e;return r?C((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(I(i,c=>{var u;(u=r.next)===null||u===void 0||u.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var u;a=!1,(u=r.error)===null||u===void 0||u.call(r,c),i.error(c)},()=>{var c,u;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(u=r.finalize)===null||u===void 0||u.call(r)}))}):q}var fa="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",_=class extends Error{code;constructor(t,n){super(Yl(t,n)),this.code=t}};function Zl(e){return`NG0${Math.abs(e)}`}function Yl(e,t){return`${Zl(e)}${t?": "+t:""}`}var pa=Symbol("InputSignalNode#UNSET"),Ql=X(J({},Qt),{transformFn:void 0,applyValueToInputSignal(e,t){gt(e,t)}});function ha(e,t){let n=Object.create(Ql);n.value=e,n.transformFn=t?.transform;function r(){if(ht(n),n.value===pa){let o=null;throw new _(-950,o)}return n.value}return r[Y]=n,r}function Vt(e){return{toString:e}.toString()}var wn="__parameters__";function Kl(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function ga(e,t,n){return Vt(()=>{let r=Kl(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,u,l){let f=c.hasOwnProperty(wn)?c[wn]:Object.defineProperty(c,wn,{value:[]})[wn];for(;f.length<=l;)f.push(null);return(f[l]=f[l]||[]).push(s),c}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var bs=globalThis;function N(e){for(let t in e)if(e[t]===N)return t;throw Error("Could not find renamed property on target object.")}function Jl(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function z(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(z).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let r=n.indexOf(`
`);return r>=0?n.slice(0,r):n}function lo(e,t){return e?t?`${e} ${t}`:e:t||""}var Xl=N({__forward_ref__:N});function ma(e){return e.__forward_ref__=ma,e.toString=function(){return z(this())},e}function B(e){return ya(e)?e():e}function ya(e){return typeof e=="function"&&e.hasOwnProperty(Xl)&&e.__forward_ref__===ma}function R(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function Da(e){return{providers:e.providers||[],imports:e.imports||[]}}function rr(e){return _s(e,va)||_s(e,Ea)}function Jw(e){return rr(e)!==null}function _s(e,t){return e.hasOwnProperty(t)?e[t]:null}function ed(e){let t=e&&(e[va]||e[Ea]);return t||null}function Ms(e){return e&&(e.hasOwnProperty(Ts)||e.hasOwnProperty(td))?e[Ts]:null}var va=N({\u0275prov:N}),Ts=N({\u0275inj:N}),Ea=N({ngInjectableDef:N}),td=N({ngInjectorDef:N}),T=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=R({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function Ia(e){return e&&!!e.\u0275providers}var nd=N({\u0275cmp:N}),rd=N({\u0275dir:N}),od=N({\u0275pipe:N}),id=N({\u0275mod:N}),An=N({\u0275fac:N}),Mt=N({__NG_ELEMENT_ID__:N}),Ss=N({__NG_ENV_ID__:N});function or(e){return typeof e=="string"?e:e==null?"":String(e)}function sd(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():or(e)}function wa(e,t){throw new _(-200,e)}function ai(e,t){throw new _(-201,!1)}var y=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(y||{}),fo;function Ca(){return fo}function Q(e){let t=fo;return fo=e,t}function ba(e,t,n){let r=rr(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&y.Optional)return null;if(t!==void 0)return t;ai(e,"Injector")}var ad={},Oe=ad,po="__NG_DI_FLAG__",Rn=class{injector;constructor(t){this.injector=t}retrieve(t,n){let r=n;return this.injector.get(t,r.optional?Kt:Oe,r)}},On="ngTempTokenPath",cd="ngTokenPath",ud=/\n/gm,ld="\u0275",Ns="__source";function dd(e,t=y.Default){if(mt()===void 0)throw new _(-203,!1);if(mt()===null)return ba(e,void 0,t);{let n=mt(),r;return n instanceof Rn?r=n.injector:r=n,r.get(e,t&y.Optional?null:void 0,t)}}function H(e,t=y.Default){return(Ca()||dd)(B(e),t)}function b(e,t=y.Default){return H(e,ir(t))}function ir(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function ho(e){let t=[];for(let n=0;n<e.length;n++){let r=B(e[n]);if(Array.isArray(r)){if(r.length===0)throw new _(900,!1);let o,i=y.Default;for(let s=0;s<r.length;s++){let a=r[s],c=fd(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(H(o,i))}else t.push(H(r))}return t}function _a(e,t){return e[po]=t,e.prototype[po]=t,e}function fd(e){return e[po]}function pd(e,t,n,r){let o=e[On];throw t[Ns]&&o.unshift(t[Ns]),e.message=hd(`
`+e.message,o,n,r),e[cd]=o,e[On]=null,e}function hd(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==ld?e.slice(2):e;let o=z(t);if(Array.isArray(t))o=t.map(z).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):z(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(ud,`
  `)}`}var gd=_a(ga("Optional"),8);var md=_a(ga("SkipSelf"),4);function Xe(e,t){let n=e.hasOwnProperty(An);return n?e[An]:null}function yd(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function Dd(e){return e.flat(Number.POSITIVE_INFINITY)}function ci(e,t){e.forEach(n=>Array.isArray(n)?ci(n,t):t(n))}function Ma(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function kn(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function vd(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function ui(e,t,n){let r=Bt(e,t);return r>=0?e[r|1]=n:(r=~r,vd(e,r,t,n)),r}function Qr(e,t){let n=Bt(e,t);if(n>=0)return e[n|1]}function Bt(e,t){return Ed(e,t,1)}function Ed(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var Fe={},U=[],Fn=new T(""),Ta=new T("",-1),Sa=new T(""),Pn=class{get(t,n=Oe){if(n===Oe){let r=new Error(`NullInjectorError: No provider for ${z(t)}!`);throw r.name="NullInjectorError",r}return n}};function Na(e,t){let n=e[id]||null;if(!n&&t===!0)throw new Error(`Type ${z(e)} does not have '\u0275mod' property.`);return n}function et(e){return e[nd]||null}function Id(e){return e[rd]||null}function wd(e){return e[od]||null}function Xw(e){return{\u0275providers:e}}function Cd(...e){return{\u0275providers:xa(!0,e),\u0275fromNgModule:!0}}function xa(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return ci(t,s=>{let a=s;go(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&Aa(o,i),n}function Aa(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];li(o,i=>{t(i,r)})}}function go(e,t,n,r){if(e=B(e),!e)return!1;let o=null,i=Ms(e),s=!i&&et(e);if(!i&&!s){let c=e.ngModule;if(i=Ms(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let u of c)go(u,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let u;try{ci(i.imports,l=>{go(l,t,n,r)&&(u||=[],u.push(l))})}finally{}u!==void 0&&Aa(u,t)}if(!a){let u=Xe(o)||(()=>new o);t({provide:o,useFactory:u,deps:U},o),t({provide:Sa,useValue:o,multi:!0},o),t({provide:Fn,useValue:()=>H(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let u=e;li(c,l=>{t(l,u)})}}else return!1;return o!==e&&e.providers!==void 0}function li(e,t){for(let n of e)Ia(n)&&(n=n.\u0275providers),Array.isArray(n)?li(n,t):t(n)}var bd=N({provide:String,useValue:N});function Ra(e){return e!==null&&typeof e=="object"&&bd in e}function _d(e){return!!(e&&e.useExisting)}function Md(e){return!!(e&&e.useFactory)}function tt(e){return typeof e=="function"}function Td(e){return!!e.useClass}var Oa=new T(""),_n={},xs={},Kr;function di(){return Kr===void 0&&(Kr=new Pn),Kr}var _e=class{},Tt=class extends _e{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,yo(t,s=>this.processProvider(s)),this.records.set(Ta,Ye(void 0,this)),o.has("environment")&&this.records.set(_e,Ye(void 0,this));let i=this.records.get(Oa);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Sa,U,y.Self))}retrieve(t,n){let r=n;return this.get(t,r.optional?Kt:Oe,r)}destroy(){bt(this),this._destroyed=!0;let t=E(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),E(t)}}onDestroy(t){return bt(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){bt(this);let n=pe(this),r=Q(void 0),o;try{return t()}finally{pe(n),Q(r)}}get(t,n=Oe,r=y.Default){if(bt(this),t.hasOwnProperty(Ss))return t[Ss](this);r=ir(r);let o,i=pe(this),s=Q(void 0);try{if(!(r&y.SkipSelf)){let c=this.records.get(t);if(c===void 0){let u=Rd(t)&&rr(t);u&&this.injectableDefInScope(u)?c=Ye(mo(t),_n):c=null,this.records.set(t,c)}if(c!=null)return this.hydrate(t,c,r)}let a=r&y.Self?di():this.parent;return n=r&y.Optional&&n===Oe?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[On]=a[On]||[]).unshift(z(t)),i)throw a;return pd(a,t,"R3InjectorError",this.source)}else throw a}finally{Q(s),pe(i)}}resolveInjectorInitializers(){let t=E(null),n=pe(this),r=Q(void 0),o;try{let i=this.get(Fn,U,y.Self);for(let s of i)s()}finally{pe(n),Q(r),E(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(z(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=B(t);let n=tt(t)?t:B(t&&t.provide),r=Nd(t);if(!tt(t)&&t.multi===!0){let o=this.records.get(n);o||(o=Ye(void 0,_n,!0),o.factory=()=>ho(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n,r){let o=E(null);try{return n.value===xs?wa(z(t)):n.value===_n&&(n.value=xs,n.value=n.factory(void 0,r)),typeof n.value=="object"&&n.value&&Ad(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{E(o)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=B(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function mo(e){let t=rr(e),n=t!==null?t.factory:Xe(e);if(n!==null)return n;if(e instanceof T)throw new _(204,!1);if(e instanceof Function)return Sd(e);throw new _(204,!1)}function Sd(e){if(e.length>0)throw new _(204,!1);let n=ed(e);return n!==null?()=>n.factory(e):()=>new e}function Nd(e){if(Ra(e))return Ye(void 0,e.useValue);{let t=ka(e);return Ye(t,_n)}}function ka(e,t,n){let r;if(tt(e)){let o=B(e);return Xe(o)||mo(o)}else if(Ra(e))r=()=>B(e.useValue);else if(Md(e))r=()=>e.useFactory(...ho(e.deps||[]));else if(_d(e))r=(o,i)=>H(B(e.useExisting),i!==void 0&&i&y.Optional?y.Optional:void 0);else{let o=B(e&&(e.useClass||e.provide));if(xd(e))r=()=>new o(...ho(e.deps));else return Xe(o)||mo(o)}return r}function bt(e){if(e.destroyed)throw new _(205,!1)}function Ye(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function xd(e){return!!e.deps}function Ad(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function Rd(e){return typeof e=="function"||typeof e=="object"&&e instanceof T}function yo(e,t){for(let n of e)Array.isArray(n)?yo(n,t):n&&Ia(n)?yo(n.\u0275providers,t):t(n)}function Fa(e,t){let n;e instanceof Tt?(bt(e),n=e):n=new Rn(e);let r,o=pe(n),i=Q(void 0);try{return t()}finally{pe(o),Q(i)}}function Od(){return Ca()!==void 0||mt()!=null}function kd(e){return typeof e=="function"}var ye=0,D=1,m=2,j=3,re=4,ie=5,Ln=6,jn=7,G=8,nt=9,he=10,k=11,St=12,As=13,at=14,ce=15,Pe=16,Qe=17,ge=18,sr=19,Pa=20,Ce=21,Jr=22,Vn=23,K=24,Xr=25,me=26,La=1;var Le=7,Bn=8,rt=9,Z=10;function be(e){return Array.isArray(e)&&typeof e[La]=="object"}function De(e){return Array.isArray(e)&&e[La]===!0}function ja(e){return(e.flags&4)!==0}function ct(e){return e.componentOffset>-1}function fi(e){return(e.flags&1)===1}function ue(e){return!!e.template}function Hn(e){return(e[m]&512)!==0}function ut(e){return(e[m]&256)===256}var Do=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function Va(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var Fd=(()=>{let e=()=>Ba;return e.ngInherit=!0,e})();function Ba(e){return e.type.prototype.ngOnChanges&&(e.setInput=Ld),Pd}function Pd(){let e=$a(this),t=e?.current;if(t){let n=e.previous;if(n===Fe)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function Ld(e,t,n,r,o){let i=this.declaredInputs[r],s=$a(e)||jd(e,{previous:Fe,current:null}),a=s.current||(s.current={}),c=s.previous,u=c[i];a[i]=new Do(u&&u.currentValue,n,c===Fe),Va(e,t,o,n)}var Ha="__ngSimpleChanges__";function $a(e){return e[Ha]||null}function jd(e,t){return e[Ha]=t}var Rs=null;var x=function(e,t=null,n){Rs?.(e,t,n)},Vd="svg",Bd="math";function le(e){for(;Array.isArray(e);)e=e[ye];return e}function Ua(e,t){return le(t[e])}function fe(e,t){return le(t[e.index])}function za(e,t){return e.data[t]}function de(e,t){let n=t[e];return be(n)?n:n[ye]}function Hd(e){return(e[m]&4)===4}function pi(e){return(e[m]&128)===128}function $d(e){return De(e[j])}function $n(e,t){return t==null?null:e[t]}function Ga(e){e[Qe]=0}function Wa(e){e[m]&1024||(e[m]|=1024,pi(e)&&cr(e))}function Ud(e,t){for(;e>0;)t=t[at],e--;return t}function ar(e){return!!(e[m]&9216||e[K]?.dirty)}function vo(e){e[he].changeDetectionScheduler?.notify(8),e[m]&64&&(e[m]|=1024),ar(e)&&cr(e)}function cr(e){e[he].changeDetectionScheduler?.notify(0);let t=je(e);for(;t!==null&&!(t[m]&8192||(t[m]|=8192,!pi(t)));)t=je(t)}function qa(e,t){if(ut(e))throw new _(911,!1);e[Ce]===null&&(e[Ce]=[]),e[Ce].push(t)}function zd(e,t){if(e[Ce]===null)return;let n=e[Ce].indexOf(t);n!==-1&&e[Ce].splice(n,1)}function je(e){let t=e[j];return De(t)?t[j]:t}function hi(e){return e[jn]??=[]}function gi(e){return e.cleanup??=[]}function Gd(e,t,n,r){let o=hi(t);o.push(n),e.firstCreatePass&&gi(e).push(r,o.length-1)}var v={lFrame:nc(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var Eo=!1;function Wd(){return v.lFrame.elementDepthCount}function qd(){v.lFrame.elementDepthCount++}function Zd(){v.lFrame.elementDepthCount--}function Za(){return v.bindingsEnabled}function Yd(){return v.skipHydrationRootTNode!==null}function Qd(e){return v.skipHydrationRootTNode===e}function Kd(){v.skipHydrationRootTNode=null}function S(){return v.lFrame.lView}function V(){return v.lFrame.tView}function eC(e){return v.lFrame.contextLView=e,e[G]}function tC(e){return v.lFrame.contextLView=null,e}function W(){let e=Ya();for(;e!==null&&e.type===64;)e=e.parent;return e}function Ya(){return v.lFrame.currentTNode}function Jd(){let e=v.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function Ht(e,t){let n=v.lFrame;n.currentTNode=e,n.isParent=t}function Qa(){return v.lFrame.isParent}function Xd(){v.lFrame.isParent=!1}function Ka(){return Eo}function Os(e){let t=Eo;return Eo=e,t}function ef(){let e=v.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function tf(e){return v.lFrame.bindingIndex=e}function ur(){return v.lFrame.bindingIndex++}function Ja(e){let t=v.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function nf(){return v.lFrame.inI18n}function rf(e,t){let n=v.lFrame;n.bindingIndex=n.bindingRootIndex=e,Io(t)}function of(){return v.lFrame.currentDirectiveIndex}function Io(e){v.lFrame.currentDirectiveIndex=e}function sf(e){let t=v.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function Xa(){return v.lFrame.currentQueryIndex}function mi(e){v.lFrame.currentQueryIndex=e}function af(e){let t=e[D];return t.type===2?t.declTNode:t.type===1?e[ie]:null}function ec(e,t,n){if(n&y.SkipSelf){let o=t,i=e;for(;o=o.parent,o===null&&!(n&y.Host);)if(o=af(i),o===null||(i=i[at],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=v.lFrame=tc();return r.currentTNode=t,r.lView=e,!0}function yi(e){let t=tc(),n=e[D];v.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function tc(){let e=v.lFrame,t=e===null?null:e.child;return t===null?nc(e):t}function nc(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function rc(){let e=v.lFrame;return v.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var oc=rc;function Di(){let e=rc();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function cf(e){return(v.lFrame.contextLView=Ud(e,v.lFrame.contextLView))[G]}function $e(){return v.lFrame.selectedIndex}function Ve(e){v.lFrame.selectedIndex=e}function vi(){let e=v.lFrame;return za(e.tView,e.selectedIndex)}function uf(){return v.lFrame.currentNamespace}var ic=!0;function Ei(){return ic}function Ii(e){ic=e}function lf(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=Ba(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function sc(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:u,ngOnDestroy:l}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),u&&((e.viewHooks??=[]).push(n,u),(e.viewCheckHooks??=[]).push(n,u)),l!=null&&(e.destroyHooks??=[]).push(n,l)}}function Mn(e,t,n){ac(e,t,3,n)}function Tn(e,t,n,r){(e[m]&3)===n&&ac(e,t,n,r)}function eo(e,t){let n=e[m];(n&3)===t&&(n&=16383,n+=1,e[m]=n)}function ac(e,t,n,r){let o=r!==void 0?e[Qe]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[Qe]+=65536),(a<i||i==-1)&&(df(e,n,t,c),e[Qe]=(e[Qe]&**********)+c+2),c++}function ks(e,t){x(4,e,t);let n=E(null);try{t.call(e)}finally{E(n),x(5,e,t)}}function df(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[m]>>14<e[Qe]>>16&&(e[m]&3)===t&&(e[m]+=16384,ks(a,i)):ks(a,i)}var Je=-1,Be=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r){this.factory=t,this.canSeeViewProviders=n,this.injectImpl=r}};function ff(e){return(e.flags&8)!==0}function pf(e){return(e.flags&16)!==0}function hf(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];gf(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function cc(e){return e===3||e===4||e===6}function gf(e){return e.charCodeAt(0)===64}function Nt(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?Fs(e,n,o,null,t[++r]):Fs(e,n,o,null,null))}}return e}function Fs(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),o!==null&&e.splice(i++,0,o)}function uc(e){return e!==Je}function Un(e){return e&32767}function mf(e){return e>>16}function zn(e,t){let n=mf(e),r=t;for(;n>0;)r=r[at],n--;return r}var wo=!0;function Ps(e){let t=wo;return wo=e,t}var yf=256,lc=yf-1,dc=5,Df=0,ae={};function vf(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(Mt)&&(r=n[Mt]),r==null&&(r=n[Mt]=Df++);let o=r&lc,i=1<<o;t.data[e+(o>>dc)]|=i}function Gn(e,t){let n=fc(e,t);if(n!==-1)return n;let r=t[D];r.firstCreatePass&&(e.injectorIndex=t.length,to(r.data,e),to(t,null),to(r.blueprint,null));let o=wi(e,t),i=e.injectorIndex;if(uc(o)){let s=Un(o),a=zn(o,t),c=a[D].data;for(let u=0;u<8;u++)t[i+u]=a[s+u]|c[s+u]}return t[i+8]=o,i}function to(e,t){e.push(0,0,0,0,0,0,0,0,t)}function fc(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function wi(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=yc(o),r===null)return Je;if(n++,o=o[at],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return Je}function Co(e,t,n){vf(e,t,n)}function Ef(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(cc(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function pc(e,t,n){if(n&y.Optional||e!==void 0)return e;ai(t,"NodeInjector")}function hc(e,t,n,r){if(n&y.Optional&&r===void 0&&(r=null),(n&(y.Self|y.Host))===0){let o=e[nt],i=Q(void 0);try{return o?o.get(t,r,n&y.Optional):ba(t,r,n&y.Optional)}finally{Q(i)}}return pc(r,t,n)}function gc(e,t,n,r=y.Default,o){if(e!==null){if(t[m]&2048&&!(r&y.Self)){let s=bf(e,t,n,r,ae);if(s!==ae)return s}let i=mc(e,t,n,r,ae);if(i!==ae)return i}return hc(t,n,r,o)}function mc(e,t,n,r,o){let i=wf(n);if(typeof i=="function"){if(!ec(t,e,r))return r&y.Host?pc(o,n,r):hc(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&y.Optional))ai(n);else return s}finally{oc()}}else if(typeof i=="number"){let s=null,a=fc(e,t),c=Je,u=r&y.Host?t[ce][ie]:null;for((a===-1||r&y.SkipSelf)&&(c=a===-1?wi(e,t):t[a+8],c===Je||!js(r,!1)?a=-1:(s=t[D],a=Un(c),t=zn(c,t)));a!==-1;){let l=t[D];if(Ls(i,a,l.data)){let f=If(a,t,n,s,r,u);if(f!==ae)return f}c=t[a+8],c!==Je&&js(r,t[D].data[a+8]===u)&&Ls(i,a,t)?(s=l,a=Un(c),t=zn(c,t)):a=-1}}return o}function If(e,t,n,r,o,i){let s=t[D],a=s.data[e+8],c=r==null?ct(a)&&wo:r!=s&&(a.type&3)!==0,u=o&y.Host&&i===a,l=Sn(a,s,n,c,u);return l!==null?xt(t,s,l,a,o):ae}function Sn(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,u=e.directiveEnd,l=i>>20,f=r?a:a+l,p=o?a+l:u;for(let d=f;d<p;d++){let h=s[d];if(d<c&&n===h||d>=c&&h.type===n)return d}if(o){let d=s[c];if(d&&ue(d)&&d.type===n)return c}return null}function xt(e,t,n,r,o){let i=e[n],s=t.data;if(i instanceof Be){let a=i;a.resolving&&wa(sd(s[n]));let c=Ps(a.canSeeViewProviders);a.resolving=!0;let u,l=a.injectImpl?Q(a.injectImpl):null,f=ec(e,r,y.Default);try{i=e[n]=a.factory(void 0,o,s,e,r),t.firstCreatePass&&n>=r.directiveStart&&lf(n,s[n],t)}finally{l!==null&&Q(l),Ps(c),a.resolving=!1,oc()}}return i}function wf(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(Mt)?e[Mt]:void 0;return typeof t=="number"?t>=0?t&lc:Cf:t}function Ls(e,t,n){let r=1<<e;return!!(n[t+(e>>dc)]&r)}function js(e,t){return!(e&y.Self)&&!(e&y.Host&&t)}var ke=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return gc(this._tNode,this._lView,t,ir(r),n)}};function Cf(){return new ke(W(),S())}function nC(e){return Vt(()=>{let t=e.prototype.constructor,n=t[An]||bo(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[An]||bo(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function bo(e){return ya(e)?()=>{let t=bo(B(e));return t&&t()}:Xe(e)}function bf(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[m]&2048&&!Hn(s);){let a=mc(i,s,n,r|y.Self,ae);if(a!==ae)return a;let c=i.parent;if(!c){let u=s[Pa];if(u){let l=u.get(n,ae,r);if(l!==ae)return l}c=yc(s),s=s[at]}i=c}return o}function yc(e){let t=e[D],n=t.type;return n===2?t.declTNode:n===1?e[ie]:null}function _f(e){return Ef(W(),e)}function Vs(e,t=null,n=null,r){let o=Dc(e,t,n,r);return o.resolveInjectorInitializers(),o}function Dc(e,t=null,n=null,r,o=new Set){let i=[n||U,Cd(e)];return r=r||(typeof e=="object"?void 0:z(e)),new Tt(i,t||di(),r||null,o)}var At=class e{static THROW_IF_NOT_FOUND=Oe;static NULL=new Pn;static create(t,n){if(Array.isArray(t))return Vs({name:""},n,t,"");{let r=t.name??"";return Vs({name:r},t.parent,t.providers,r)}}static \u0275prov=R({token:e,providedIn:"any",factory:()=>H(Ta)});static __NG_ELEMENT_ID__=-1};var Mf=new T("");Mf.__NG_ELEMENT_ID__=e=>{let t=W();if(t===null)throw new _(204,!1);if(t.type&2)return t.value;if(e&y.Optional)return null;throw new _(204,!1)};var vc=!1,Ec=(()=>{class e{static __NG_ELEMENT_ID__=Tf;static __NG_ENV_ID__=n=>n}return e})(),_o=class extends Ec{_lView;constructor(t){super(),this._lView=t}onDestroy(t){let n=this._lView;return ut(n)?(t(),()=>{}):(qa(n,t),()=>zd(n,t))}};function Tf(){return new _o(S())}var Rt=class{},Ic=new T("",{providedIn:"root",factory:()=>!1});var wc=new T(""),Cc=new T(""),lr=(()=>{class e{taskId=0;pendingTasks=new Set;get _hasPendingTasks(){return this.hasPendingTasks.value}hasPendingTasks=new vt(!1);add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static \u0275prov=R({token:e,providedIn:"root",factory:()=>new e})}return e})();var Mo=class extends se{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,Od()&&(this.destroyRef=b(Ec,{optional:!0})??void 0,this.pendingTasks=b(lr,{optional:!0})??void 0)}emit(t){let n=E(null);try{super.next(t)}finally{E(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof L&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{t(n)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},we=Mo;function Wn(...e){}function bc(e){let t,n;function r(){e=Wn;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function Bs(e){return queueMicrotask(()=>e()),()=>{e=Wn}}var Ci="isAngularZone",qn=Ci+"_ID",Sf=0,oe=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new we(!1);onMicrotaskEmpty=new we(!1);onStable=new we(!1);onError=new we(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=vc}=t;if(typeof Zone>"u")throw new _(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,Af(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(Ci)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new _(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new _(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,Nf,Wn,Wn);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},Nf={};function bi(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function xf(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){bc(()=>{e.callbackScheduled=!1,To(e),e.isCheckStableRunning=!0,bi(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),To(e)}function Af(e){let t=()=>{xf(e)},n=Sf++;e._inner=e._inner.fork({name:"angular",properties:{[Ci]:!0,[qn]:n,[qn+n]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(Rf(c))return r.invokeTask(i,s,a,c);try{return Hs(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),$s(e)}},onInvoke:(r,o,i,s,a,c,u)=>{try{return Hs(e),r.invoke(i,s,a,c,u)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!Of(c)&&t(),$s(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,To(e),bi(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function To(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function Hs(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function $s(e){e._nesting--,bi(e)}var So=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new we;onMicrotaskEmpty=new we;onStable=new we;onError=new we;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function Rf(e){return _c(e,"__ignore_ng_zone__")}function Of(e){return _c(e,"__scheduler_tick__")}function _c(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}var ot=class{_console=console;handleError(t){this._console.error("ERROR",t)}},kf=new T("",{providedIn:"root",factory:()=>{let e=b(oe),t=b(ot);return n=>e.runOutsideAngular(()=>t.handleError(n))}});function Us(e,t){return ha(e,t)}function Ff(e){return ha(pa,e)}var rC=(Us.required=Ff,Us);function Pf(){return lt(W(),S())}function lt(e,t){return new $t(fe(e,t))}var $t=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=Pf}return e})();function Lf(e){return e instanceof $t?e.nativeElement:e}function jf(e){return typeof e=="function"&&e[Y]!==void 0}function oC(e,t){let n=Or(e,t?.equal),r=n[Y];return n.set=o=>gt(r,o),n.update=o=>kr(r,o),n.asReadonly=Vf.bind(n),n}function Vf(){let e=this[Y];if(e.readonlyFn===void 0){let t=()=>this();t[Y]=e,e.readonlyFn=t}return e.readonlyFn}function Mc(e){return jf(e)&&typeof e.set=="function"}function Bf(){return this._results[Symbol.iterator]()}var No=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new se}constructor(t=!1){this._emitDistinctChangesOnly=t}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=Dd(t);(this._changesDetected=!yd(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=Bf};function Tc(e){return(e.flags&128)===128}var Sc=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Sc||{}),Nc=new Map,Hf=0;function $f(){return Hf++}function Uf(e){Nc.set(e[sr],e)}function xo(e){Nc.delete(e[sr])}var zs="__ngContext__";function Ut(e,t){be(t)?(e[zs]=t[sr],Uf(t)):e[zs]=t}function xc(e){return Rc(e[St])}function Ac(e){return Rc(e[re])}function Rc(e){for(;e!==null&&!De(e);)e=e[re];return e}var Ao;function iC(e){Ao=e}function zf(){if(Ao!==void 0)return Ao;if(typeof document<"u")return document;throw new _(210,!1)}var sC=new T("",{providedIn:"root",factory:()=>Gf}),Gf="ng",Wf=new T(""),aC=new T("",{providedIn:"platform",factory:()=>"unknown"});var cC=new T("",{providedIn:"root",factory:()=>zf().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var qf="h",Zf="b";var Oc=!1,Yf=new T("",{providedIn:"root",factory:()=>Oc});var kc=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(kc||{}),Fc=new T(""),Gs=new Set;function Qf(e){Gs.has(e)||(Gs.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var Kf=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=R({token:e,providedIn:"root",factory:()=>new e})}return e})();var Jf=(e,t,n,r)=>{};function Xf(e,t,n,r){Jf(e,t,n,r)}var ep=()=>null;function Pc(e,t,n=!1){return ep(e,t,n)}function Lc(e,t){let n=e.contentQueries;if(n!==null){let r=E(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];mi(i),a.contentQueries(2,t[s],s)}}}finally{E(r)}}}function Ro(e,t,n){mi(0);let r=E(null);try{t(e,n)}finally{E(r)}}function jc(e,t,n){if(ja(t)){let r=E(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{E(r)}}}var Ot=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(Ot||{});var Cn;function tp(){if(Cn===void 0&&(Cn=null,bs.trustedTypes))try{Cn=bs.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return Cn}function Ws(e){return tp()?.createScriptURL(e)||e}var Zn=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${fa})`}};function dr(e){return e instanceof Zn?e.changingThisBreaksApplicationSecurity:e}function Vc(e,t){let n=np(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${fa})`)}return n===t}function np(e){return e instanceof Zn&&e.getTypeName()||null}var rp=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function op(e){return e=String(e),e.match(rp)?e:"unsafe:"+e}var _i=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(_i||{});function ip(e){let t=Bc();return t?t.sanitize(_i.URL,e)||"":Vc(e,"URL")?dr(e):op(or(e))}function sp(e){let t=Bc();if(t)return Ws(t.sanitize(_i.RESOURCE_URL,e)||"");if(Vc(e,"ResourceURL"))return Ws(dr(e));throw new _(904,!1)}function ap(e,t){return t==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||t==="href"&&(e==="base"||e==="link")?sp:ip}function uC(e,t,n){return ap(t,n)(e)}function Bc(){let e=S();return e&&e[he].sanitizer}function Hc(e){return e instanceof Function?e():e}function cp(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var $c="ng-template";function up(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&cp(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(Mi(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function Mi(e){return e.type===4&&e.value!==$c}function lp(e,t,n){let r=e.type===4&&!n?$c:e.value;return t===r}function dp(e,t,n){let r=4,o=e.attrs,i=o!==null?hp(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!te(r)&&!te(c))return!1;if(s&&te(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!lp(e,c,n)||c===""&&t.length===1){if(te(r))return!1;s=!0}}else if(r&8){if(o===null||!up(e,o,c,n)){if(te(r))return!1;s=!0}}else{let u=t[++a],l=fp(c,o,Mi(e),n);if(l===-1){if(te(r))return!1;s=!0;continue}if(u!==""){let f;if(l>i?f="":f=o[l+1].toLowerCase(),r&2&&u!==f){if(te(r))return!1;s=!0}}}}return te(r)||s}function te(e){return(e&1)===0}function fp(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return gp(t,e)}function pp(e,t,n=!1){for(let r=0;r<t.length;r++)if(dp(e,t[r],n))return!0;return!1}function hp(e){for(let t=0;t<e.length;t++){let n=e[t];if(cc(n))return t}return e.length}function gp(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function qs(e,t){return e?":not("+t.trim()+")":t}function mp(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!te(s)&&(t+=qs(i,o),o=""),r=s,i=i||!te(r);n++}return o!==""&&(t+=qs(i,o)),t}function yp(e){return e.map(mp).join(",")}function Dp(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!te(o))break;o=i}r++}return n.length&&t.push(1,...n),t}var ve={};function vp(e,t){return e.createText(t)}function Ep(e,t,n){e.setValue(t,n)}function Uc(e,t,n){return e.createElement(t,n)}function Yn(e,t,n,r,o){e.insertBefore(t,n,r,o)}function zc(e,t,n){e.appendChild(t,n)}function Zs(e,t,n,r,o){r!==null?Yn(e,t,n,r,o):zc(e,t,n)}function Ip(e,t,n){e.removeChild(null,t,n)}function wp(e,t,n){e.setAttribute(t,"style",n)}function Cp(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function Gc(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&hf(e,t,r),o!==null&&Cp(e,t,o),i!==null&&wp(e,t,i)}function Ti(e,t,n,r,o,i,s,a,c,u,l){let f=me+r,p=f+o,d=bp(f,p),h=typeof u=="function"?u():u;return d[D]={type:e,blueprint:d,template:n,queries:null,viewQuery:a,declTNode:t,data:d.slice().fill(null,f),bindingStartIndex:f,expandoStartIndex:p,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:h,incompleteFirstPass:!1,ssrId:l}}function bp(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:ve);return n}function _p(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=Ti(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Si(e,t,n,r,o,i,s,a,c,u,l){let f=t.blueprint.slice();return f[ye]=o,f[m]=r|4|128|8|64|1024,(u!==null||e&&e[m]&2048)&&(f[m]|=2048),Ga(f),f[j]=f[at]=e,f[G]=n,f[he]=s||e&&e[he],f[k]=a||e&&e[k],f[nt]=c||e&&e[nt]||null,f[ie]=i,f[sr]=$f(),f[Ln]=l,f[Pa]=u,f[ce]=t.type==2?e[ce]:f,f}function Mp(e,t,n){let r=fe(t,e),o=_p(n),i=e[he].rendererFactory,s=Ni(e,Si(e,o,null,Wc(n),r,t,null,i.createRenderer(r,n),null,null,null));return e[t.index]=s}function Wc(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function qc(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function Ni(e,t){return e[St]?e[As][re]=t:e[St]=t,e[As]=t,t}function lC(e=1){Zc(V(),S(),$e()+e,!1)}function Zc(e,t,n,r){if(!r)if((t[m]&3)===3){let i=e.preOrderCheckHooks;i!==null&&Mn(t,i,n)}else{let i=e.preOrderHooks;i!==null&&Tn(t,i,0,n)}Ve(n)}var fr=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(fr||{});function Oo(e,t,n,r){let o=E(null);try{let[i,s,a]=e.inputs[n],c=null;(s&fr.SignalBased)!==0&&(c=t[i][Y]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(t,r)),e.setInput!==null?e.setInput(t,c,r,n,i):Va(t,c,i,r)}finally{E(o)}}function Yc(e,t,n,r,o){let i=$e(),s=r&2;try{Ve(-1),s&&t.length>me&&Zc(e,t,me,!1),x(s?2:0,o),n(r,o)}finally{Ve(i),x(s?3:1,o)}}function xi(e,t,n){Rp(e,t,n),(n.flags&64)===64&&Op(e,t,n)}function Qc(e,t,n=fe){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function Tp(e,t,n,r){let i=r.get(Yf,Oc)||n===Ot.ShadowDom,s=e.selectRootElement(t,i);return Sp(s),s}function Sp(e){Np(e)}var Np=()=>null;function xp(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function Kc(e,t,n,r,o,i,s,a){if(!a&&Ai(t,e,n,r,o)){ct(t)&&Ap(n,t.index);return}if(t.type&3){let c=fe(t,n);r=xp(r),o=s!=null?s(o,t.value||"",r):o,i.setProperty(c,r,o)}else t.type&12}function Ap(e,t){let n=de(t,e);n[m]&16||(n[m]|=64)}function Rp(e,t,n){let r=n.directiveStart,o=n.directiveEnd;ct(n)&&Mp(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||Gn(n,t);let i=n.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=xt(t,e,s,n);if(Ut(c,t),i!==null&&Lp(t,s-r,c,a,n,i),ue(a)){let u=de(n.index,t);u[G]=xt(t,e,s,n)}}}function Op(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=of();try{Ve(i);for(let a=r;a<o;a++){let c=e.data[a],u=t[a];Io(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&kp(c,u)}}finally{Ve(-1),Io(s)}}function kp(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function Jc(e,t){let n=e.directiveRegistry,r=null;if(n)for(let o=0;o<n.length;o++){let i=n[o];pp(t,i.selectors,!1)&&(r??=[],ue(i)?r.unshift(i):r.push(i))}return r}function Fp(e,t,n,r,o,i){let s=fe(e,t);Pp(t[k],s,i,e.value,n,r,o)}function Pp(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?or(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function Lp(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],u=s[a+1];Oo(r,n,c,u)}}function jp(e,t){let n=e[nt],r=n?n.get(ot,null):null;r&&r.handleError(t)}function Ai(e,t,n,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let u=s[c],l=s[c+1],f=t.data[u];Oo(f,n[u],l,o),a=!0}if(i)for(let c of i){let u=n[c],l=t.data[c];Oo(l,u,r,o),a=!0}return a}function Vp(e,t){let n=de(t,e),r=n[D];Bp(r,n);let o=n[ye];o!==null&&n[Ln]===null&&(n[Ln]=Pc(o,n[nt])),x(18),Ri(r,n,n[G]),x(19,n[G])}function Bp(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function Ri(e,t,n){yi(t);try{let r=e.viewQuery;r!==null&&Ro(1,r,n);let o=e.template;o!==null&&Yc(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[ge]?.finishViewCreation(e),e.staticContentQueries&&Lc(e,t),e.staticViewQueries&&Ro(2,e.viewQuery,n);let i=e.components;i!==null&&Hp(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[m]&=-5,Di()}}function Hp(e,t){for(let n=0;n<t.length;n++)Vp(e,t[n])}function $p(e,t,n,r){let o=E(null);try{let i=t.tView,a=e[m]&4096?4096:16,c=Si(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),u=e[t.index];c[Pe]=u;let l=e[ge];return l!==null&&(c[ge]=l.createEmbeddedView(i)),Ri(i,c,n),c}finally{E(o)}}function Ys(e,t){return!t||t.firstChild===null||Tc(e)}var Up;function Oi(e,t){return Up(e,t)}var ko=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(ko||{});function Xc(e){return(e.flags&32)===32}function Ke(e,t,n,r,o){if(r!=null){let i,s=!1;De(r)?i=r:be(r)&&(s=!0,r=r[ye]);let a=le(r);e===0&&n!==null?o==null?zc(t,n,a):Yn(t,n,a,o||null,!0):e===1&&n!==null?Yn(t,n,a,o||null,!0):e===2?Ip(t,a,s):e===3&&t.destroyNode(a),i!=null&&th(t,e,i,n,o)}}function zp(e,t){eu(e,t),t[ye]=null,t[ie]=null}function Gp(e,t,n,r,o,i){r[ye]=o,r[ie]=t,pr(e,r,n,1,o,i)}function eu(e,t){t[he].changeDetectionScheduler?.notify(9),pr(e,t,t[k],2,null,null)}function Wp(e){let t=e[St];if(!t)return no(e[D],e);for(;t;){let n=null;if(be(t))n=t[St];else{let r=t[Z];r&&(n=r)}if(!n){for(;t&&!t[re]&&t!==e;)be(t)&&no(t[D],t),t=t[j];t===null&&(t=e),be(t)&&no(t[D],t),n=t&&t[re]}t=n}}function ki(e,t){let n=e[rt],r=n.indexOf(t);n.splice(r,1)}function tu(e,t){if(ut(t))return;let n=t[k];n.destroyNode&&pr(e,t,n,3,null,null),Wp(t)}function no(e,t){if(ut(t))return;let n=E(null);try{t[m]&=-129,t[m]|=256,t[K]&&xr(t[K]),Zp(e,t),qp(e,t),t[D].type===1&&t[k].destroy();let r=t[Pe];if(r!==null&&De(t[j])){r!==t[j]&&ki(r,t);let o=t[ge];o!==null&&o.detachView(e)}xo(t)}finally{E(n)}}function qp(e,t){let n=e.cleanup,r=t[jn];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[jn]=null);let o=t[Ce];if(o!==null){t[Ce]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[Vn];if(i!==null){t[Vn]=null;for(let s of i)s.destroy()}}function Zp(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof Be)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];x(4,a,c);try{c.call(a)}finally{x(5,a,c)}}else{x(4,o,i);try{i.call(o)}finally{x(5,o,i)}}}}}function Yp(e,t,n){return Qp(e,t.parent,n)}function Qp(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[ye];if(ct(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===Ot.None||o===Ot.Emulated)return null}return fe(r,n)}function Kp(e,t,n){return Xp(e,t,n)}function Jp(e,t,n){return e.type&40?fe(e,n):null}var Xp=Jp,Qs;function Fi(e,t,n,r){let o=Yp(e,r,t),i=t[k],s=r.parent||t[ie],a=Kp(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)Zs(i,o,n[c],a,!1);else Zs(i,o,n,a,!1);Qs!==void 0&&Qs(i,r,t,n,o)}function _t(e,t){if(t!==null){let n=t.type;if(n&3)return fe(t,e);if(n&4)return Fo(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return _t(e,r);{let o=e[t.index];return De(o)?Fo(-1,o):le(o)}}else{if(n&128)return _t(e,t.next);if(n&32)return Oi(t,e)()||le(e[t.index]);{let r=nu(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=je(e[ce]);return _t(o,r)}else return _t(e,t.next)}}}return null}function nu(e,t){if(t!==null){let r=e[ce][ie],o=t.projection;return r.projection[o]}return null}function Fo(e,t){let n=Z+e+1;if(n<t.length){let r=t[n],o=r[D].firstChild;if(o!==null)return _t(r,o)}return t[Le]}function Pi(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&Ut(le(a),r),n.flags|=2),!Xc(n))if(c&8)Pi(e,t,n.child,r,o,i,!1),Ke(t,e,o,a,i);else if(c&32){let u=Oi(n,r),l;for(;l=u();)Ke(t,e,o,l,i);Ke(t,e,o,a,i)}else c&16?eh(e,t,r,n,o,i):Ke(t,e,o,a,i);n=s?n.projectionNext:n.next}}function pr(e,t,n,r,o,i){Pi(n,r,e.firstChild,t,o,i,!1)}function eh(e,t,n,r,o,i){let s=n[ce],c=s[ie].projection[r.projection];if(Array.isArray(c))for(let u=0;u<c.length;u++){let l=c[u];Ke(t,e,o,l,i)}else{let u=c,l=s[j];Tc(r)&&(u.flags|=128),Pi(e,t,u,l,o,i,!0)}}function th(e,t,n,r,o){let i=n[Le],s=le(n);i!==s&&Ke(t,e,r,i,o);for(let a=Z;a<n.length;a++){let c=n[a];pr(c[D],c,e,t,r,i)}}function nh(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:ko.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=ko.Important),e.setStyle(n,r,o,i))}}function Qn(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(le(i)),De(i)&&rh(i,r);let s=n.type;if(s&8)Qn(e,t,n.child,r);else if(s&32){let a=Oi(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=nu(t,n);if(Array.isArray(a))r.push(...a);else{let c=je(t[ce]);Qn(c[D],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function rh(e,t){for(let n=Z;n<e.length;n++){let r=e[n],o=r[D].firstChild;o!==null&&Qn(r[D],r,o,t)}e[Le]!==e[ye]&&t.push(e[Le])}function ru(e){if(e[Xr]!==null){for(let t of e[Xr])t.impl.addSequence(t);e[Xr].length=0}}var ou=[];function oh(e){return e[K]??ih(e)}function ih(e){let t=ou.pop()??Object.create(ah);return t.lView=e,t}function sh(e){e.lView[K]!==e&&(e.lView=null,ou.push(e))}var ah=X(J({},pt),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{cr(e.lView)},consumerOnSignalRead(){this.lView[K]=this}});function ch(e){let t=e[K]??Object.create(uh);return t.lView=e,t}var uh=X(J({},pt),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=je(e.lView);for(;t&&!iu(t[D]);)t=je(t);t&&Wa(t)},consumerOnSignalRead(){this.lView[K]=this}});function iu(e){return e.type!==2}function su(e){if(e[Vn]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[Vn])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[m]&8192)}}var lh=100;function au(e,t=!0,n=0){let o=e[he].rendererFactory,i=!1;i||o.begin?.();try{dh(e,n)}catch(s){throw t&&jp(e,s),s}finally{i||o.end?.()}}function dh(e,t){let n=Ka();try{Os(!0),Po(e,t);let r=0;for(;ar(e);){if(r===lh)throw new _(103,!1);r++,Po(e,1)}}finally{Os(n)}}function fh(e,t,n,r){if(ut(t))return;let o=t[m],i=!1,s=!1;yi(t);let a=!0,c=null,u=null;i||(iu(e)?(u=oh(t),c=qt(u)):br()===null?(a=!1,u=ch(t),c=qt(u)):t[K]&&(xr(t[K]),t[K]=null));try{Ga(t),tf(e.bindingStartIndex),n!==null&&Yc(e,t,n,2,r);let l=(o&3)===3;if(!i)if(l){let d=e.preOrderCheckHooks;d!==null&&Mn(t,d,null)}else{let d=e.preOrderHooks;d!==null&&Tn(t,d,0,null),eo(t,0)}if(s||ph(t),su(t),cu(t,0),e.contentQueries!==null&&Lc(e,t),!i)if(l){let d=e.contentCheckHooks;d!==null&&Mn(t,d)}else{let d=e.contentHooks;d!==null&&Tn(t,d,1),eo(t,1)}gh(e,t);let f=e.components;f!==null&&lu(t,f,0);let p=e.viewQuery;if(p!==null&&Ro(2,p,r),!i)if(l){let d=e.viewCheckHooks;d!==null&&Mn(t,d)}else{let d=e.viewHooks;d!==null&&Tn(t,d,2),eo(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[Jr]){for(let d of t[Jr])d();t[Jr]=null}i||(ru(t),t[m]&=-73)}catch(l){throw i||cr(t),l}finally{u!==null&&(Sr(u,c),a&&sh(u)),Di()}}function cu(e,t){for(let n=xc(e);n!==null;n=Ac(n))for(let r=Z;r<n.length;r++){let o=n[r];uu(o,t)}}function ph(e){for(let t=xc(e);t!==null;t=Ac(t)){if(!(t[m]&2))continue;let n=t[rt];for(let r=0;r<n.length;r++){let o=n[r];Wa(o)}}}function hh(e,t,n){x(18);let r=de(t,e);uu(r,n),x(19,r[G])}function uu(e,t){pi(e)&&Po(e,t)}function Po(e,t){let r=e[D],o=e[m],i=e[K],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&Nr(i)),s||=!1,i&&(i.dirty=!1),e[m]&=-9217,s)fh(r,e,r.template,e[G]);else if(o&8192){su(e),cu(e,1);let a=r.components;a!==null&&lu(e,a,1),ru(e)}}function lu(e,t,n){for(let r=0;r<t.length;r++)hh(e,t[r],n)}function gh(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)Ve(~o);else{let i=o,s=n[++r],a=n[++r];rf(s,i);let c=t[i];x(24,c),a(2,c),x(25,c)}}}finally{Ve(-1)}}function Li(e,t){let n=Ka()?64:1088;for(e[he].changeDetectionScheduler?.notify(t);e;){e[m]|=n;let r=je(e);if(Hn(e)&&!r)return e;e=r}return null}function du(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function mh(e,t,n,r=!0){let o=t[D];if(yh(o,t,e,n),r){let s=Fo(n,e),a=t[k],c=a.parentNode(e[Le]);c!==null&&Gp(o,e[ie],a,t,c,s)}let i=t[Ln];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function Lo(e,t){if(e.length<=Z)return;let n=Z+t,r=e[n];if(r){let o=r[Pe];o!==null&&o!==e&&ki(o,r),t>0&&(e[n-1][re]=r[re]);let i=kn(e,Z+t);zp(r[D],r);let s=i[ge];s!==null&&s.detachView(i[D]),r[j]=null,r[re]=null,r[m]&=-129}return r}function yh(e,t,n,r){let o=Z+r,i=n.length;r>0&&(n[o-1][re]=t),r<i-Z?(t[re]=n[o],Ma(n,Z+r,t)):(n.push(t),t[re]=null),t[j]=n;let s=t[Pe];s!==null&&n!==s&&fu(s,t);let a=t[ge];a!==null&&a.insertView(e),vo(t),t[m]|=128}function fu(e,t){let n=e[rt],r=t[j];if(be(r))e[m]|=2;else{let o=r[j][ce];t[ce]!==o&&(e[m]|=2)}n===null?e[rt]=[t]:n.push(t)}var kt=class{_lView;_cdRefInjectingView;notifyErrorHandler;_appRef=null;_attachedToViewContainer=!1;get rootNodes(){let t=this._lView,n=t[D];return Qn(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r}get context(){return this._lView[G]}set context(t){this._lView[G]=t}get destroyed(){return ut(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[j];if(De(t)){let n=t[Bn],r=n?n.indexOf(this):-1;r>-1&&(Lo(t,r),kn(n,r))}this._attachedToViewContainer=!1}tu(this._lView[D],this._lView)}onDestroy(t){qa(this._lView,t)}markForCheck(){Li(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[m]&=-129}reattach(){vo(this._lView),this._lView[m]|=128}detectChanges(){this._lView[m]|=1024,au(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new _(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=Hn(this._lView),n=this._lView[Pe];n!==null&&!t&&ki(n,this._lView),eu(this._lView[D],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new _(902,!1);this._appRef=t;let n=Hn(this._lView),r=this._lView[Pe];r!==null&&!n&&fu(r,this._lView),vo(this._lView)}};var it=(()=>{class e{static __NG_ELEMENT_ID__=Eh}return e})(),Dh=it,vh=class extends Dh{_declarationLView;_declarationTContainer;elementRef;constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let o=$p(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new kt(o)}};function Eh(){return ji(W(),S())}function ji(e,t){return e.type&4?new vh(t,e,lt(e,t)):null}function Vi(e,t,n,r,o){let i=e.data[t];if(i===null)i=Ih(e,t,n,r,o),nf()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=Jd();i.injectorIndex=s===null?-1:s.injectorIndex}return Ht(i,!0),i}function Ih(e,t,n,r,o){let i=Ya(),s=Qa(),a=s?i:i&&i.parent,c=e.data[t]=Ch(e,a,n,t,r,o);return wh(e,c,i,s),c}function wh(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function Ch(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return Yd()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var pC=new RegExp(`^(\\d+)*(${Zf}|${qf})*(.*)`);var bh=()=>null;function Ks(e,t){return bh(e,t)}var _h=class{},pu=class{},jo=class{resolveComponentFactory(t){throw Error(`No component factory found for ${z(t)}.`)}},hr=class{static NULL=new jo},Kn=class{},Mh=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>Th()}return e})();function Th(){let e=S(),t=W(),n=de(t.index,e);return(be(n)?n:e)[k]}var Sh=(()=>{class e{static \u0275prov=R({token:e,providedIn:"root",factory:()=>null})}return e})();var ro={},Vo=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=ir(r);let o=this.injector.get(t,ro,r);return o!==ro||n===ro?o:this.parentInjector.get(t,n,r)}};function Js(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=lo(o,a);else if(i==2){let c=a,u=t[++s];r=lo(r,c+": "+u+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function Ue(e,t=y.Default){let n=S();if(n===null)return H(e,t);let r=W();return gc(r,n,B(e),t)}function hu(e,t,n,r,o){let i=r===null?null:{"":-1},s=o(e,n);if(s!==null){let a,c=null,u=null,l=xh(s);l===null?a=s:[a,c,u]=l,Oh(e,t,n,a,i,c,u)}i!==null&&r!==null&&Nh(n,r,i)}function Nh(e,t,n){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new _(-301,!1);r.push(t[o],i)}}function xh(e){let t=null,n=!1;for(let s=0;s<e.length;s++){let a=e[s];if(s===0&&ue(a)&&(t=a),a.findHostDirectiveDefs!==null){n=!0;break}}if(!n)return null;let r=null,o=null,i=null;for(let s of e)s.findHostDirectiveDefs!==null&&(r??=[],o??=new Map,i??=new Map,Ah(s,r,i,o)),s===t&&(r??=[],r.push(s));return r!==null?(r.push(...t===null?e:e.slice(1)),[r,o,i]):null}function Ah(e,t,n,r){let o=t.length;e.findHostDirectiveDefs(e,t,r),n.set(e,[o,t.length-1])}function Rh(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function Oh(e,t,n,r,o,i,s){let a=r.length,c=!1;for(let p=0;p<a;p++){let d=r[p];!c&&ue(d)&&(c=!0,Rh(e,n,p)),Co(Gn(n,t),e,d.type)}Vh(n,e.data.length,a);for(let p=0;p<a;p++){let d=r[p];d.providersResolver&&d.providersResolver(d)}let u=!1,l=!1,f=qc(e,t,a,null);a>0&&(n.directiveToIndex=new Map);for(let p=0;p<a;p++){let d=r[p];if(n.mergedAttrs=Nt(n.mergedAttrs,d.hostAttrs),Fh(e,n,t,f,d),jh(f,d,o),s!==null&&s.has(d)){let[w,P]=s.get(d);n.directiveToIndex.set(d.type,[f,w+n.directiveStart,P+n.directiveStart])}else(i===null||!i.has(d))&&n.directiveToIndex.set(d.type,f);d.contentQueries!==null&&(n.flags|=4),(d.hostBindings!==null||d.hostAttrs!==null||d.hostVars!==0)&&(n.flags|=64);let h=d.type.prototype;!u&&(h.ngOnChanges||h.ngOnInit||h.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),u=!0),!l&&(h.ngOnChanges||h.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),l=!0),f++}kh(e,n,i)}function kh(e,t,n){for(let r=t.directiveStart;r<t.directiveEnd;r++){let o=e.data[r];if(n===null||!n.has(o))Xs(0,t,o,r),Xs(1,t,o,r),ta(t,r,!1);else{let i=n.get(o);ea(0,t,i,r),ea(1,t,i,r),ta(t,r,!0)}}}function Xs(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=t.inputs??={}:s=t.outputs??={},s[i]??=[],s[i].push(r),gu(t,i)}}function ea(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=t.hostDirectiveInputs??={}:a=t.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),gu(t,s)}}function gu(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function ta(e,t,n){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!n&&o===null||n&&i===null||Mi(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!n&&o.hasOwnProperty(c)){let u=o[c];for(let l of u)if(l===t){s??=[],s.push(c,r[a+1]);break}}else if(n&&i.hasOwnProperty(c)){let u=i[c];for(let l=0;l<u.length;l+=2)if(u[l]===t){s??=[],s.push(u[l+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function Fh(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=Xe(o.type,!0)),s=new Be(i,ue(o),Ue);e.blueprint[r]=s,n[r]=s,Ph(e,t,r,qc(e,n,o.hostVars,ve),o)}function Ph(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;Lh(s)!=a&&s.push(a),s.push(n,r,i)}}function Lh(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function jh(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;ue(t)&&(n[""]=e)}}function Vh(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function mu(e,t,n,r,o,i,s,a){let c=t.consts,u=$n(c,s),l=Vi(t,e,2,r,u);return i&&hu(t,n,l,$n(c,a),o),l.mergedAttrs=Nt(l.mergedAttrs,l.attrs),l.attrs!==null&&Js(l,l.attrs,!1),l.mergedAttrs!==null&&Js(l,l.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,l),l}function yu(e,t){sc(e,t),ja(t)&&e.queries.elementEnd(t)}var Jn=class extends hr{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=et(t);return new Ft(n,this.ngModule)}};function Bh(e){return Object.keys(e).map(t=>{let[n,r,o]=e[t],i={propName:n,templateName:t,isSignal:(r&fr.SignalBased)!==0};return o&&(i.transform=o),i})}function Hh(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function $h(e,t,n){let r=t instanceof _e?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new Vo(n,r):n}function Uh(e){let t=e.get(Kn,null);if(t===null)throw new _(407,!1);let n=e.get(Sh,null),r=e.get(Rt,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r}}function zh(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return Uc(t,n,n==="svg"?Vd:n==="math"?Bd:null)}var Ft=class extends pu{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=Bh(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=Hh(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=yp(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,o){x(22);let i=E(null);try{let s=this.componentDef,a=r?["ng-version","19.2.14"]:Dp(this.componentDef.selectors[0]),c=Ti(0,null,null,1,0,null,null,null,null,[a],null),u=$h(s,o||this.ngModule,t),l=Uh(u),f=l.rendererFactory.createRenderer(null,s),p=r?Tp(f,r,s.encapsulation,u):zh(s,f),d=Si(null,c,null,512|Wc(s),null,null,l,f,u,null,Pc(p,u,!0));d[me]=p,yi(d);let h=null;try{let w=mu(me,c,d,"#host",()=>[this.componentDef],!0,0);p&&(Gc(f,p,w),Ut(p,d)),xi(c,d,w),jc(c,w,d),yu(c,w),n!==void 0&&Gh(w,this.ngContentSelectors,n),h=de(w.index,d),d[G]=h[G],Ri(c,d,null)}catch(w){throw h!==null&&xo(h),xo(d),w}finally{x(23),Di()}return new Bo(this.componentType,d)}finally{E(i)}}},Bo=class extends _h{_rootLView;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n){super(),this._rootLView=n,this._tNode=za(n[D],me),this.location=lt(this._tNode,n),this.instance=de(this._tNode.index,n)[G],this.hostView=this.changeDetectorRef=new kt(n,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView,i=Ai(r,o[D],o,t,n);this.previousInputValues.set(t,n);let s=de(r.index,o);Li(s,1)}get injector(){return new ke(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function Gh(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}var zt=(()=>{class e{static __NG_ELEMENT_ID__=Wh}return e})();function Wh(){let e=W();return vu(e,S())}var qh=zt,Du=class extends qh{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return lt(this._hostTNode,this._hostLView)}get injector(){return new ke(this._hostTNode,this._hostLView)}get parentInjector(){let t=wi(this._hostTNode,this._hostLView);if(uc(t)){let n=zn(t,this._hostLView),r=Un(t),o=n[D].data[r+8];return new ke(o,n)}else return new ke(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=na(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-Z}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=Ks(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,Ys(this._hostTNode,s)),a}createComponent(t,n,r,o,i){let s=t&&!kd(t),a;if(s)a=n;else{let h=n||{};a=h.index,r=h.injector,o=h.projectableNodes,i=h.environmentInjector||h.ngModuleRef}let c=s?t:new Ft(et(t)),u=r||this.parentInjector;if(!i&&c.ngModule==null){let w=(s?u:this.parentInjector).get(_e,null);w&&(i=w)}let l=et(c.componentType??{}),f=Ks(this._lContainer,l?.id??null),p=f?.firstChild??null,d=c.create(u,o,p,i);return this.insertImpl(d.hostView,a,Ys(this._hostTNode,f)),d}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if($d(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[j],u=new Du(c,c[ie],c[j]);u.detach(u.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return mh(s,o,i,r),t.attachToViewContainerRef(),Ma(oo(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=na(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=Lo(this._lContainer,n);r&&(kn(oo(this._lContainer),n),tu(r[D],r))}detach(t){let n=this._adjustIndex(t,-1),r=Lo(this._lContainer,n);return r&&kn(oo(this._lContainer),n)!=null?new kt(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function na(e){return e[Bn]}function oo(e){return e[Bn]||(e[Bn]=[])}function vu(e,t){let n,r=t[e.index];return De(r)?n=r:(n=du(r,t,null,e),t[e.index]=n,Ni(t,n)),Yh(n,t,e,r),new Du(n,e,t)}function Zh(e,t){let n=e[k],r=n.createComment(""),o=fe(t,e),i=n.parentNode(o);return Yn(n,i,r,n.nextSibling(o),!1),r}var Yh=Jh,Qh=()=>!1;function Kh(e,t,n){return Qh(e,t,n)}function Jh(e,t,n,r){if(e[Le])return;let o;n.type&8?o=le(r):o=Zh(t,n),e[Le]=o}var Ho=class e{queryList;matches=null;constructor(t){this.queryList=t}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},$o=class e{queries;constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)Bi(t,n).matches!==null&&this.queries[n].setDirty()}},Uo=class{flags;read;predicate;constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=sg(t):this.predicate=t}},zo=class e{queries;constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},Go=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(t,n=-1){this.metadata=t,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(t,n,Xh(n,i)),this.matchTNodeWithReadOption(t,n,Sn(n,t,i,!1,!1))}else r===it?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,Sn(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===$t||o===zt||o===it&&n.type&4)this.addMatch(n.index,-2);else{let i=Sn(n,t,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function Xh(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function eg(e,t){return e.type&11?lt(e,t):e.type&4?ji(e,t):null}function tg(e,t,n,r){return n===-1?eg(t,e):n===-2?ng(e,t,r):xt(e,e[D],n,t)}function ng(e,t,n){if(n===$t)return lt(t,e);if(n===it)return ji(t,e);if(n===zt)return vu(t,e)}function Eu(e,t,n,r){let o=t[ge].queries[r];if(o.matches===null){let i=e.data,s=n.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let u=s[c];if(u<0)a.push(null);else{let l=i[u];a.push(tg(t,l,s[c+1],n.metadata.read))}}o.matches=a}return o.matches}function Wo(e,t,n,r){let o=e.queries.getByIndex(n),i=o.matches;if(i!==null){let s=Eu(e,t,o,n);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)r.push(s[a/2]);else{let u=i[a+1],l=t[-c];for(let f=Z;f<l.length;f++){let p=l[f];p[Pe]===p[j]&&Wo(p[D],p,u,r)}if(l[rt]!==null){let f=l[rt];for(let p=0;p<f.length;p++){let d=f[p];Wo(d[D],d,u,r)}}}}}return r}function rg(e,t){return e[ge].queries[t].queryList}function og(e,t,n){let r=new No((n&4)===4);return Gd(e,t,r,r.destroy),(t[ge]??=new $o).queries.push(new Ho(r))-1}function ig(e,t,n,r){let o=V();if(o.firstCreatePass){let i=W();ag(o,new Uo(t,n,r),i.index),cg(o,e),(n&2)===2&&(o.staticContentQueries=!0)}return og(o,S(),n)}function sg(e){return e.split(",").map(t=>t.trim())}function ag(e,t,n){e.queries===null&&(e.queries=new zo),e.queries.track(new Go(t,n))}function cg(e,t){let n=e.contentQueries||(e.contentQueries=[]),r=n.length?n[n.length-1]:-1;t!==r&&n.push(e.queries.length-1,t)}function Bi(e,t){return e.queries.getByIndex(t)}function ug(e,t){let n=e[D],r=Bi(n,t);return r.crossesNgTemplate?Wo(n,e,t,[]):Eu(n,e,r,t)}var Pt=class{},lg=class{};var qo=class extends Pt{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new Jn(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=Na(t);this._bootstrapComponents=Hc(i.bootstrap),this._r3Injector=Dc(t,n,[{provide:Pt,useValue:this},{provide:hr,useValue:this.componentFactoryResolver},...r],z(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},Zo=class extends lg{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new qo(this.moduleType,t,[])}};var Xn=class extends Pt{injector;componentFactoryResolver=new Jn(this);instance=null;constructor(t){super();let n=new Tt([...t.providers,{provide:Pt,useValue:this},{provide:hr,useValue:this.componentFactoryResolver}],t.parent||di(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function dg(e,t,n=null){return new Xn({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var fg=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=xa(!1,n.type),o=r.length>0?dg([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=R({token:e,providedIn:"environment",factory:()=>new e(H(_e))})}return e})();function EC(e){return Vt(()=>{let t=Cu(e),n=X(J({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Sc.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get(fg).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||Ot.Emulated,styles:e.styles||U,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&Qf("NgStandalone"),bu(n);let r=e.dependencies;return n.directiveDefs=ra(r,!1),n.pipeDefs=ra(r,!0),n.id=yg(n),n})}function pg(e){return et(e)||Id(e)}function hg(e){return e!==null}function Iu(e){return Vt(()=>({type:e.type,bootstrap:e.bootstrap||U,declarations:e.declarations||U,imports:e.imports||U,exports:e.exports||U,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function gg(e,t){if(e==null)return Fe;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=fr.None,c=null),n[i]=[r,a,c],t[i]=s}return n}function mg(e){if(e==null)return Fe;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function wu(e){return Vt(()=>{let t=Cu(e);return bu(t),t})}function Cu(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||Fe,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||U,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:gg(e.inputs,t),outputs:mg(e.outputs),debugInfo:null}}function bu(e){e.features?.forEach(t=>t(e))}function ra(e,t){if(!e)return null;let n=t?wd:pg;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(hg)}function yg(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function Dg(e){return Object.getPrototypeOf(e.prototype).constructor}function vg(e){let t=Dg(e.type),n=!0,r=[e];for(;t;){let o;if(ue(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new _(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=io(e.inputs),s.declaredInputs=io(e.declaredInputs),s.outputs=io(e.outputs);let a=o.hostBindings;a&&bg(e,a);let c=o.viewQuery,u=o.contentQueries;if(c&&wg(e,c),u&&Cg(e,u),Eg(e,o),Jl(e.outputs,o.outputs),ue(o)&&o.data.animation){let l=e.data;l.animation=(l.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===vg&&(n=!1)}}t=Object.getPrototypeOf(t)}Ig(r)}function Eg(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n])}}function Ig(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=Nt(o.hostAttrs,n=Nt(n,o.hostAttrs))}}function io(e){return e===Fe?{}:e===U?[]:e}function wg(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function Cg(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function bg(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function _u(e){return Mg(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function _g(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function Mg(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function Tg(e,t,n){return e[t]=n}function Sg(e,t){return e[t]}function dt(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function Ng(e,t,n,r,o,i,s,a,c){let u=t.consts,l=Vi(t,e,4,s||null,a||null);Za()&&hu(t,n,l,$n(u,c),Jc),l.mergedAttrs=Nt(l.mergedAttrs,l.attrs),sc(t,l);let f=l.tView=Ti(2,l,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,u,null);return t.queries!==null&&(t.queries.template(t,l),f.queries=t.queries.embeddedTView(l)),l}function xg(e,t,n,r,o,i,s,a,c,u){let l=n+me,f=t.firstCreatePass?Ng(l,t,e,r,o,i,s,a,c):t.data[l];Ht(f,!1);let p=Rg(t,e,f,n);Ei()&&Fi(t,e,p,f),Ut(p,e);let d=du(p,e,p,f);return e[l]=d,Ni(e,d),Kh(d,f,e),fi(f)&&xi(t,e,f),c!=null&&Qc(e,f,u),f}function Ag(e,t,n,r,o,i,s,a){let c=S(),u=V(),l=$n(u.consts,i);return xg(c,u,e,t,n,r,o,l,s,a),Ag}var Rg=Og;function Og(e,t,n,r){return Ii(!0),t[k].createComment("")}var IC=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=R({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var kg=new T("");var Fg=(()=>{class e{static \u0275prov=R({token:e,providedIn:"root",factory:()=>new Yo})}return e})(),Yo=class{queuedEffectCount=0;queues=new Map;schedule(t){this.enqueue(t)}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),this.queuedEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||(this.queuedEffectCount++,r.add(t))}flush(){for(;this.queuedEffectCount>0;)for(let[t,n]of this.queues)t===null?this.flushQueue(n):t.run(()=>this.flushQueue(n))}flushQueue(t){for(let n of t)t.delete(n),this.queuedEffectCount--,n.run()}};function Mu(e){return!!e&&typeof e.then=="function"}function Pg(e){return!!e&&typeof e.subscribe=="function"}var Lg=new T("");var Tu=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=b(Lg,{optional:!0})??[];injector=b(At);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=Fa(this.injector,o);if(Mu(i))n.push(i);else if(Pg(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=R({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),jg=new T("");function Vg(){Rr(()=>{throw new _(600,!1)})}function Bg(e){return e.isBoundToModule}var Hg=10;var Lt=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=b(kf);afterRenderManager=b(Kf);zonelessEnabled=b(Ic);rootEffectScheduler=b(Fg);dirtyFlags=0;tracingSnapshot=null;externalTestViews=new Set;afterTick=new se;get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];isStable=b(lr).hasPendingTasks.pipe(Ae(n=>!n));constructor(){b(Fc,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=b(_e);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){return this.bootstrapImpl(n,r)}bootstrapImpl(n,r,o=At.NULL){x(10);let i=n instanceof pu;if(!this._injector.get(Tu).done){let d="";throw new _(405,d)}let a;i?a=n:a=this._injector.get(hr).resolveComponentFactory(n),this.componentTypes.push(a.componentType);let c=Bg(a)?void 0:this._injector.get(Pt),u=r||a.selector,l=a.create(o,[],u,c),f=l.location.nativeElement,p=l.injector.get(kg,null);return p?.registerApplication(f),l.onDestroy(()=>{this.detachView(l.hostView),Nn(this.components,l),p?.unregisterApplication(f)}),this._loadComponent(l),x(11,l),l}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){x(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(kc.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new _(101,!1);let n=E(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,E(n),this.afterTick.next(),x(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(Kn,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<Hg;)x(14),this.synchronizeOnce(),x(15)}synchronizeOnce(){if(this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush()),this.dirtyFlags&7){let n=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:r,notifyErrorHandler:o}of this.allViews)$g(r,o,n,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}else this._rendererFactory?.begin?.(),this._rendererFactory?.end?.();this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>ar(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;Nn(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n),this._injector.get(jg,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>Nn(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new _(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=R({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Nn(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function $g(e,t,n,r){if(!n&&!ar(e))return;au(e,t,n&&!r?0:1)}function Ug(e,t,n,r){let o=S(),i=ur();if(dt(o,i,t)){let s=V(),a=vi();Fp(a,o,e,t,n,r)}return Ug}function zg(e,t,n,r){return dt(e,ur(),n)?t+or(n)+r:ve}function bn(e,t){return e<<17|t<<2}function He(e){return e>>17&32767}function Gg(e){return(e&2)==2}function Wg(e,t){return e&131071|t<<17}function Qo(e){return e|2}function st(e){return(e&131068)>>2}function so(e,t){return e&-131069|t<<2}function qg(e){return(e&1)===1}function Ko(e){return e|1}function Zg(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=He(s),c=st(s);e[r]=n;let u=!1,l;if(Array.isArray(n)){let f=n;l=f[1],(l===null||Bt(f,l)>0)&&(u=!0)}else l=n;if(o)if(c!==0){let p=He(e[a+1]);e[r+1]=bn(p,a),p!==0&&(e[p+1]=so(e[p+1],r)),e[a+1]=Wg(e[a+1],r)}else e[r+1]=bn(a,0),a!==0&&(e[a+1]=so(e[a+1],r)),a=r;else e[r+1]=bn(c,0),a===0?a=r:e[c+1]=so(e[c+1],r),c=r;u&&(e[r+1]=Qo(e[r+1])),oa(e,l,r,!0),oa(e,l,r,!1),Yg(t,l,e,r,i),s=bn(a,c),i?t.classBindings=s:t.styleBindings=s}function Yg(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&Bt(i,t)>=0&&(n[r+1]=Ko(n[r+1]))}function oa(e,t,n,r){let o=e[n+1],i=t===null,s=r?He(o):st(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],u=e[s+1];Qg(c,t)&&(a=!0,e[s+1]=r?Ko(u):Qo(u)),s=r?He(u):st(u)}a&&(e[n+1]=r?Qo(o):Ko(o))}function Qg(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?Bt(e,t)>=0:!1}var ne={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function Kg(e){return e.substring(ne.key,ne.keyEnd)}function Jg(e){return Xg(e),Su(e,Nu(e,0,ne.textEnd))}function Su(e,t){let n=ne.textEnd;return n===t?-1:(t=ne.keyEnd=em(e,ne.key=t,n),Nu(e,t,n))}function Xg(e){ne.key=0,ne.keyEnd=0,ne.value=0,ne.valueEnd=0,ne.textEnd=e.length}function Nu(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function em(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}function tm(e,t,n){let r=S(),o=ur();if(dt(r,o,t)){let i=V(),s=vi();Kc(i,s,r,e,t,r[k],n,!1)}return tm}function Jo(e,t,n,r,o){Ai(t,e,n,o?"class":"style",r)}function nm(e,t){return om(e,t,null,!0),nm}function wC(e){im(dm,rm,e,!0)}function rm(e,t){for(let n=Jg(t);n>=0;n=Su(t,n))ui(e,Kg(t),!0)}function om(e,t,n,r){let o=S(),i=V(),s=Ja(2);if(i.firstUpdatePass&&Au(i,e,s,r),t!==ve&&dt(o,s,t)){let a=i.data[$e()];Ru(i,a,o,o[k],e,o[s+1]=pm(t,n),r,s)}}function im(e,t,n,r){let o=V(),i=Ja(2);o.firstUpdatePass&&Au(o,null,i,r);let s=S();if(n!==ve&&dt(s,i,n)){let a=o.data[$e()];if(Ou(a,r)&&!xu(o,i)){let c=r?a.classesWithoutHost:a.stylesWithoutHost;c!==null&&(n=lo(c,n||"")),Jo(o,a,s,n,r)}else fm(o,a,s,s[k],s[i+1],s[i+1]=lm(e,t,n),r,i)}}function xu(e,t){return t>=e.expandoStartIndex}function Au(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[$e()],s=xu(e,n);Ou(i,r)&&t===null&&!s&&(t=!1),t=sm(o,i,t,r),Zg(o,i,t,n,s,r)}}function sm(e,t,n,r){let o=sf(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=ao(null,e,t,n,r),n=jt(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=ao(o,e,t,n,r),i===null){let c=am(e,t,r);c!==void 0&&Array.isArray(c)&&(c=ao(null,e,t,c[1],r),c=jt(c,t.attrs,r),cm(e,t,r,c))}else i=um(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function am(e,t,n){let r=n?t.classBindings:t.styleBindings;if(st(r)!==0)return e[He(r)]}function cm(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[He(o)]=r}function um(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=jt(r,s,n)}return jt(r,t.attrs,n)}function ao(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=jt(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function jt(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),ui(e,s,n?!0:t[++i]))}return e===void 0?null:e}function lm(e,t,n){if(n==null||n==="")return U;let r=[],o=dr(n);if(Array.isArray(o))for(let i=0;i<o.length;i++)e(r,o[i],!0);else if(typeof o=="object")for(let i in o)o.hasOwnProperty(i)&&e(r,i,o[i]);else typeof o=="string"&&t(r,o);return r}function dm(e,t,n){let r=String(t);r!==""&&!r.includes(" ")&&ui(e,r,n)}function fm(e,t,n,r,o,i,s,a){o===ve&&(o=U);let c=0,u=0,l=0<o.length?o[0]:null,f=0<i.length?i[0]:null;for(;l!==null||f!==null;){let p=c<o.length?o[c+1]:void 0,d=u<i.length?i[u+1]:void 0,h=null,w;l===f?(c+=2,u+=2,p!==d&&(h=f,w=d)):f===null||l!==null&&l<f?(c+=2,h=l):(u+=2,h=f,w=d),h!==null&&Ru(e,t,n,r,h,w,s,a),l=c<o.length?o[c]:null,f=u<i.length?i[u]:null}}function Ru(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let c=e.data,u=c[a+1],l=qg(u)?ia(c,t,n,o,st(u),s):void 0;if(!er(l)){er(i)||Gg(u)&&(i=ia(c,null,n,o,a,s));let f=Ua($e(),n);nh(r,s,f,o,i)}}function ia(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let c=e[o],u=Array.isArray(c),l=u?c[1]:c,f=l===null,p=n[o+1];p===ve&&(p=f?U:void 0);let d=f?Qr(p,r):l===r?p:void 0;if(u&&!er(d)&&(d=Qr(c,r)),er(d)&&(a=d,s))return a;let h=e[o+1];o=s?He(h):st(h)}if(t!==null){let c=i?t.residualClasses:t.residualStyles;c!=null&&(a=Qr(c,r))}return a}function er(e){return e!==void 0}function pm(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=z(dr(e)))),e}function Ou(e,t){return(e.flags&(t?8:16))!==0}function ku(e,t,n,r){let o=S(),i=V(),s=me+e,a=o[k],c=i.firstCreatePass?mu(s,i,o,t,Jc,Za(),n,r):i.data[s],u=gm(i,o,c,a,t,e);o[s]=u;let l=fi(c);return Ht(c,!0),Gc(a,u,c),!Xc(c)&&Ei()&&Fi(i,o,u,c),(Wd()===0||l)&&Ut(u,o),qd(),l&&(xi(i,o,c),jc(i,c,o)),r!==null&&Qc(o,c),ku}function Fu(){let e=W();Qa()?Xd():(e=e.parent,Ht(e,!1));let t=e;Qd(t)&&Kd(),Zd();let n=V();return n.firstCreatePass&&yu(n,t),t.classesWithoutHost!=null&&ff(t)&&Jo(n,t,S(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&pf(t)&&Jo(n,t,S(),t.stylesWithoutHost,!1),Fu}function hm(e,t,n,r){return ku(e,t,n,r),Fu(),hm}var gm=(e,t,n,r,o,i)=>(Ii(!0),Uc(r,o,uf()));function CC(){return S()}var tr="en-US";var mm=tr;function ym(e){typeof e=="string"&&(mm=e.toLowerCase().replace(/_/g,"-"))}function sa(e,t,n){return function r(o){if(o===Function)return n;let i=ct(e)?de(e.index,t):t;Li(i,5);let s=t[G],a=aa(t,s,n,o),c=r.__ngNextListenerFn__;for(;c;)a=aa(t,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function aa(e,t,n,r){let o=E(null);try{return x(6,t,n),n(r)!==!1}catch(i){return Dm(e,i),!1}finally{x(7,t,n),E(o)}}function Dm(e,t){let n=e[nt],r=n?n.get(ot,null):null;r&&r.handleError(t)}function ca(e,t,n,r,o,i){let s=t[n],a=t[D],u=a.data[n].outputs[r],l=s[u],f=a.firstCreatePass?gi(a):null,p=hi(t),d=l.subscribe(i),h=p.length;p.push(i,d),f&&f.push(o,e.index,h,-(h+1))}function vm(e,t,n,r){let o=S(),i=V(),s=W();return Pu(i,o,o[k],s,e,t,r),vm}function Em(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[jn],c=o[i+2];return a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function Pu(e,t,n,r,o,i,s){let a=fi(r),u=e.firstCreatePass?gi(e):null,l=hi(t),f=!0;if(r.type&3||s){let p=fe(r,t),d=s?s(p):p,h=l.length,w=s?A=>s(le(A[r.index])):r.index,P=null;if(!s&&a&&(P=Em(e,t,o,r.index)),P!==null){let A=P.__ngLastListenerFn__||P;A.__ngNextListenerFn__=i,P.__ngLastListenerFn__=i,f=!1}else{i=sa(r,t,i),Xf(t,d,o,i);let A=n.listen(d,o,i);l.push(i,A),u&&u.push(o,w,h,h+1)}}else i=sa(r,t,i);if(f){let p=r.outputs?.[o],d=r.hostDirectiveOutputs?.[o];if(d&&d.length)for(let h=0;h<d.length;h+=2){let w=d[h],P=d[h+1];ca(r,t,w,P,o,i)}if(p&&p.length)for(let h of p)ca(r,t,h,o,o,i)}}function bC(e=1){return cf(e)}function _C(e,t,n,r){ig(e,t,n,r)}function MC(e){let t=S(),n=V(),r=Xa();mi(r+1);let o=Bi(n,r);if(e.dirty&&Hd(t)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=ug(t,r);e.reset(i,Lf),e.notifyOnChanges()}return!0}return!1}function TC(){return rg(S(),Xa())}function SC(e,t=""){let n=S(),r=V(),o=e+me,i=r.firstCreatePass?Vi(r,o,1,t,null):r.data[o],s=Im(r,n,i,t,e);n[o]=s,Ei()&&Fi(r,n,s,i),Ht(i,!1)}var Im=(e,t,n,r,o)=>(Ii(!0),vp(t[k],r));function wm(e){return Lu("",e,""),wm}function Lu(e,t,n){let r=S(),o=zg(r,e,t,n);return o!==ve&&Cm(r,$e(),o),Lu}function Cm(e,t,n){let r=Ua(t,e);Ep(e[k],r,n)}function bm(e,t,n){Mc(t)&&(t=t());let r=S(),o=ur();if(dt(r,o,t)){let i=V(),s=vi();Kc(i,s,r,e,t,r[k],n,!1)}return bm}function NC(e,t){let n=Mc(e);return n&&e.set(t),n}function _m(e,t){let n=S(),r=V(),o=W();return Pu(r,n,n[k],o,e,t),_m}function Mm(e,t,n){let r=V();if(r.firstCreatePass){let o=ue(e);Xo(n,r.data,r.blueprint,o,!0),Xo(t,r.data,r.blueprint,o,!1)}}function Xo(e,t,n,r,o){if(e=B(e),Array.isArray(e))for(let i=0;i<e.length;i++)Xo(e[i],t,n,r,o);else{let i=V(),s=S(),a=W(),c=tt(e)?e:B(e.provide),u=ka(e),l=a.providerIndexes&1048575,f=a.directiveStart,p=a.providerIndexes>>20;if(tt(e)||!e.multi){let d=new Be(u,o,Ue),h=uo(c,t,o?l:l+p,f);h===-1?(Co(Gn(a,s),i,c),co(i,e,t.length),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(d),s.push(d)):(n[h]=d,s[h]=d)}else{let d=uo(c,t,l+p,f),h=uo(c,t,l,l+p),w=d>=0&&n[d],P=h>=0&&n[h];if(o&&!P||!o&&!w){Co(Gn(a,s),i,c);let A=Nm(o?Sm:Tm,n.length,o,r,u);!o&&P&&(n[h].providerFactory=A),co(i,e,t.length,0),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(A),s.push(A)}else{let A=ju(n[o?h:d],u,!o&&r);co(i,e,d>-1?d:h,A)}!o&&r&&P&&n[h].componentProviders++}}}function co(e,t,n,r){let o=tt(t),i=Td(t);if(o||i){let c=(i?B(t.useClass):t).prototype.ngOnDestroy;if(c){let u=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let l=u.indexOf(n);l===-1?u.push(n,[r,c]):u[l+1].push(r,c)}else u.push(n,c)}}}function ju(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function uo(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function Tm(e,t,n,r,o){return ei(this.multi,[])}function Sm(e,t,n,r,o){let i=this.multi,s;if(this.providerFactory){let a=this.providerFactory.componentProviders,c=xt(r,r[D],this.providerFactory.index,o);s=c.slice(0,a),ei(i,s);for(let u=a;u<c.length;u++)s.push(c[u])}else s=[],ei(i,s);return s}function ei(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function Nm(e,t,n,r,o){let i=new Be(e,n,Ue);return i.multi=[],i.index=t,i.componentProviders=0,ju(i,o,r&&!n),i}function xC(e,t=[]){return n=>{n.providersResolver=(r,o)=>Mm(r,o?o(e):e,t)}}function AC(e,t,n){let r=ef()+e,o=S();return o[r]===ve?Tg(o,r,n?t.call(n):t()):Sg(o,r)}var ti=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},RC=(()=>{class e{compileModuleSync(n){return new Zo(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=Na(n),i=Hc(o.declarations).reduce((s,a)=>{let c=et(a);return c&&s.push(new Ft(c)),s},[]);return new ti(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=R({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var xm=(()=>{class e{zone=b(oe);changeDetectionScheduler=b(Rt);applicationRef=b(Lt);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=R({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Am({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new oe(X(J({},Rm()),{scheduleInRootZone:n})),[{provide:oe,useFactory:e},{provide:Fn,multi:!0,useFactory:()=>{let r=b(xm,{optional:!0});return()=>r.initialize()}},{provide:Fn,multi:!0,useFactory:()=>{let r=b(Om);return()=>{r.initialize()}}},t===!0?{provide:wc,useValue:!0}:[],{provide:Cc,useValue:n??vc}]}function Rm(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var Om=(()=>{class e{subscription=new L;initialized=!1;zone=b(oe);pendingTasks=b(lr);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{oe.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{oe.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=R({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var km=(()=>{class e{appRef=b(Lt);taskService=b(lr);ngZone=b(oe);zonelessEnabled=b(Ic);tracing=b(Fc,{optional:!0});disableScheduling=b(wc,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new L;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(qn):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(b(Cc,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof So||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?Bs:bc;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(qn+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(n),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,Bs(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=R({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Fm(){return typeof $localize<"u"&&$localize.locale||tr}var Vu=new T("",{providedIn:"root",factory:()=>b(Vu,y.Optional|y.SkipSelf)||Fm()});var ni=new T(""),Pm=new T("");function Ct(e){return!e.moduleRef}function Lm(e){let t=Ct(e)?e.r3Injector:e.moduleRef.injector,n=t.get(oe);return n.run(()=>{Ct(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(ot,null),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:i=>{r.handleError(i)}})}),Ct(e)){let i=()=>t.destroy(),s=e.platformInjector.get(ni);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(ni);s.add(i),e.moduleRef.onDestroy(()=>{Nn(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return Vm(r,n,()=>{let i=t.get(Tu);return i.runInitializers(),i.donePromise.then(()=>{let s=t.get(Vu,tr);if(ym(s||tr),!t.get(Pm,!0))return Ct(e)?t.get(Lt):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(Ct(e)){let c=t.get(Lt);return e.rootComponent!==void 0&&c.bootstrap(e.rootComponent),c}else return jm(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}function jm(e,t){let n=e.injector.get(Lt);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>n.bootstrap(r));else if(e.instance.ngDoBootstrap)e.instance.ngDoBootstrap(n);else throw new _(-403,!1);t.push(e)}function Vm(e,t,n){try{let r=n();return Mu(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}var xn=null;function Bm(e=[],t){return At.create({name:t,providers:[{provide:Oa,useValue:"platform"},{provide:ni,useValue:new Set([()=>xn=null])},...e]})}function Hm(e=[]){if(xn)return xn;let t=Bm(e);return xn=t,Vg(),$m(t),t}function $m(e){let t=e.get(Wf,null);Fa(e,()=>{t?.forEach(n=>n())})}var Um=(()=>{class e{static __NG_ELEMENT_ID__=zm}return e})();function zm(e){return Gm(W(),S(),(e&16)===16)}function Gm(e,t,n){if(ct(e)&&!n){let r=de(e.index,t);return new kt(r,r)}else if(e.type&175){let r=t[ce];return new kt(r,t)}return null}var ri=class{constructor(){}supports(t){return _u(t)}create(t){return new oi(t)}},Wm=(e,t)=>t,oi=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(t){this._trackByFn=t||Wm}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<ua(r,o,i)?n:r,a=ua(s,o,i),c=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let u=a-o,l=c-o;if(u!=l){for(let p=0;p<u;p++){let d=p<i.length?i[p]:i[p]=0,h=d+p;l<=h&&h<u&&(i[p]=d+1)}let f=s.previousIndex;i[f]=l-u}}a!==c&&t(s,a,c)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!_u(t))throw new _(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,_g(t,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return t===null?i=this._itTail:(i=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,o),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new ii(n,r),i,o)),t}_verifyReinsertion(t,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let o=t._prevRemoved,i=t._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let o=n===null?this._itHead:n._next;return t._next=o,t._prev=n,o===null?this._itTail=t:o._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new nr),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new nr),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},ii=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(t,n){this.item=t,this.trackById=n}},si=class{_head=null;_tail=null;add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},nr=class{map=new Map;put(t){let n=t.trackById,r=this.map.get(n);r||(r=new si,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,o=this.map.get(r);return o?o.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function ua(e,t,n){let r=e.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}function la(){return new Hi([new ri])}var Hi=(()=>{class e{factories;static \u0275prov=R({token:e,providedIn:"root",factory:la});constructor(n){this.factories=n}static create(n,r){if(r!=null){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||la()),deps:[[e,new md,new gd]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r!=null)return r;throw new _(901,!1)}}return e})();function OC(e){x(8);try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=Hm(r),i=[Am({}),{provide:Rt,useExisting:km},...n||[]],s=new Xn({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return Lm({r3Injector:s.injector,platformInjector:o,rootComponent:t})}catch(t){return Promise.reject(t)}finally{x(9)}}function kC(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function FC(e){return Fr(e)}function PC(e,t){return Ar(e,t?.equal)}var da=class{[Y];constructor(t){this[Y]=t}destroy(){this[Y].destroy()}};var gr=new T("");var Uu=null;function mr(){return Uu}function qm(e){Uu??=e}var $i=class{},Ui=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=R({token:e,factory:()=>b(zu),providedIn:"platform"})}return e})();var zu=(()=>{class e extends Ui{_location;_history;_doc=b(gr);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return mr().getBaseHref(this._doc)}onPopState(n){let r=mr().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=mr().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=R({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function Gu(e,t){return e?t?e.endsWith("/")?t.startsWith("/")?e+t.slice(1):e+t:t.startsWith("/")?e+t:`${e}/${t}`:e:t}function Hu(e){let t=e.search(/#|\?|$/);return e[t-1]==="/"?e.slice(0,t-1)+e.slice(t):e}function Me(e){return e&&e[0]!=="?"?`?${e}`:e}var yr=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=R({token:e,factory:()=>b(qu),providedIn:"root"})}return e})(),Wu=new T(""),qu=(()=>{class e extends yr{_platformLocation;_baseHref;_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??b(gr).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return Gu(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+Me(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+Me(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+Me(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(H(Ui),H(Wu,8))};static \u0275prov=R({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Zu=(()=>{class e{_subject=new se;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(n){this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=Qm(Hu($u(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+Me(r))}normalize(n){return e.stripTrailingSlash(Ym(this._basePath,$u(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+Me(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+Me(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=Me;static joinWithSlash=Gu;static stripTrailingSlash=Hu;static \u0275fac=function(r){return new(r||e)(H(yr))};static \u0275prov=R({token:e,factory:()=>Zm(),providedIn:"root"})}return e})();function Zm(){return new Zu(H(yr))}function Ym(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function $u(e){return e.replace(/\/index.html$/,"")}function Qm(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}var Dr=class{$implicit;ngForOf;index;count;constructor(t,n,r,o){this.$implicit=t,this.ngForOf=n,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},Qu=(()=>{class e{_viewContainer;_template;_differs;set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(n,r,o){this._viewContainer=n,this._template=r,this._differs=o}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;!this._differ&&n&&(this._differ=this._differs.find(n).create(this.ngForTrackBy))}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let r=this._viewContainer;n.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new Dr(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),Yu(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}n.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);Yu(i,o)})}static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(Ue(zt),Ue(it),Ue(Hi))};static \u0275dir=wu({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}})}return e})();function Yu(e,t){e.context.$implicit=t.item}var Km=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=Iu({type:e});static \u0275inj=Da({})}return e})();function Jm(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var Xm="browser",Ku="server";function ey(e){return e===Ku}var zi=class{};export{J as a,X as b,L as c,hl as d,Gr as e,Wr as f,se as g,vt as h,Et as i,Ie as j,bl as k,_l as l,Ml as m,xe as n,Ae as o,kl as p,Re as q,qr as r,En as s,Pl as t,Ll as u,It as v,ws as w,jl as x,wt as y,Zr as z,Bl as A,Hl as B,Yr as C,$l as D,Ul as E,zl as F,Gl as G,Wl as H,ql as I,_ as J,ma as K,R as L,Da as M,Jw as N,T as O,y as P,H as Q,b as R,Xw as S,Oa as T,_e as U,Fa as V,Fd as W,eC as X,tC as Y,nC as Z,_f as _,At as $,Ec as aa,lr as ba,we as ca,oe as da,ot as ea,rC as fa,$t as ga,oC as ha,iC as ia,sC as ja,Wf as ka,aC as la,cC as ma,Fc as na,Ot as oa,uC as pa,lC as qa,ko as ra,Kn as sa,Mh as ta,Ue as ua,zt as va,lg as wa,dg as xa,EC as ya,Iu as za,wu as Aa,vg as Ba,Ag as Ca,IC as Da,Mu as Ea,jg as Fa,Lt as Ga,Ug as Ha,tm as Ia,nm as Ja,wC as Ka,ku as La,Fu as Ma,hm as Na,CC as Oa,vm as Pa,bC as Qa,_C as Ra,MC as Sa,TC as Ta,SC as Ua,wm as Va,Lu as Wa,bm as Xa,NC as Ya,_m as Za,xC as _a,AC as $a,RC as ab,Um as bb,OC as cb,kC as db,FC as eb,PC as fb,gr as gb,mr as hb,qm as ib,$i as jb,yr as kb,Zu as lb,Qu as mb,Km as nb,Jm as ob,Xm as pb,ey as qb,zi as rb};
