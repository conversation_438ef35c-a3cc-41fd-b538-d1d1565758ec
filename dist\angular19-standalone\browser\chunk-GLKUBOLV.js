import{La as n,Ma as e,Ua as t,Wa as m,nb as d,qa as o,ya as a}from"./chunk-XF3IHJ6T.js";var l=class r{constructor(){this.buildDate=new Date().toLocaleDateString()}static{this.\u0275fac=function(i){return new(i||r)}}static{this.\u0275cmp=a({type:r,selectors:[["app-about"]],decls:116,vars:1,consts:[[1,"about"],[1,"card"],[1,"tech-stack"],[1,"tech-grid"],[1,"tech-item"],[1,"features-section"],[1,"features-grid"],[1,"feature-card"],[1,"architecture-section"],[1,"architecture-info"],[1,"arch-item"],[1,"version-info"],[1,"version-grid"],[1,"version-item"]],template:function(i,c){i&1&&(n(0,"div",0)(1,"div",1)(2,"h2"),t(3,"About This Application"),e(),n(4,"p"),t(5,"This is a demonstration of Angular 19's standalone components feature."),e(),n(6,"div",2)(7,"h3"),t(8,"Technology Stack"),e(),n(9,"div",3)(10,"div",4)(11,"h4"),t(12,"Angular 19"),e(),n(13,"p"),t(14,"Latest version with standalone components"),e()(),n(15,"div",4)(16,"h4"),t(17,"TypeScript"),e(),n(18,"p"),t(19,"Type-safe JavaScript development"),e()(),n(20,"div",4)(21,"h4"),t(22,"Signals"),e(),n(23,"p"),t(24,"New reactive state management"),e()(),n(25,"div",4)(26,"h4"),t(27,"Standalone Components"),e(),n(28,"p"),t(29,"No NgModules required"),e()()()(),n(30,"div",5)(31,"h3"),t(32,"Features Demonstrated"),e(),n(33,"div",6)(34,"div",7)(35,"h4"),t(36,"\u{1F680} Standalone Components"),e(),n(37,"p"),t(38,"Components that can import their own dependencies without NgModules"),e()(),n(39,"div",7)(40,"h4"),t(41,"\u{1F4E1} Signals"),e(),n(42,"p"),t(43,"New reactive state management system for better performance"),e()(),n(44,"div",7)(45,"h4"),t(46,"\u{1F504} Lazy Loading"),e(),n(47,"p"),t(48,"Route-based code splitting with standalone components"),e()(),n(49,"div",7)(50,"h4"),t(51,"\u{1F3AF} Modern Routing"),e(),n(52,"p"),t(53,"Simplified routing configuration with functional guards"),e()(),n(54,"div",7)(55,"h4"),t(56,"\u{1F4F1} Responsive Design"),e(),n(57,"p"),t(58,"Mobile-first CSS with modern layout techniques"),e()(),n(59,"div",7)(60,"h4"),t(61,"\u26A1 Performance"),e(),n(62,"p"),t(63,"Optimized bundle size and runtime performance"),e()()()(),n(64,"div",8)(65,"h3"),t(66,"Application Architecture"),e(),n(67,"div",9)(68,"div",10)(69,"h4"),t(70,"Component Structure"),e(),n(71,"ul")(72,"li"),t(73,"App Component (Root)"),e(),n(74,"li"),t(75,"Home Component (Landing page)"),e(),n(76,"li"),t(77,"Counter Component (Signals demo)"),e(),n(78,"li"),t(79,"Users Component (List management)"),e(),n(80,"li"),t(81,"About Component (This page)"),e()()(),n(82,"div",10)(83,"h4"),t(84,"Key Benefits"),e(),n(85,"ul")(86,"li"),t(87,"Simplified architecture"),e(),n(88,"li"),t(89,"Better tree-shaking"),e(),n(90,"li"),t(91,"Reduced boilerplate"),e(),n(92,"li"),t(93,"Improved developer experience"),e(),n(94,"li"),t(95,"Better performance"),e()()()()(),n(96,"div",11)(97,"h3"),t(98,"Version Information"),e(),n(99,"div",12)(100,"div",13)(101,"strong"),t(102,"Angular:"),e(),t(103," 19.x "),e(),n(104,"div",13)(105,"strong"),t(106,"TypeScript:"),e(),t(107," 5.6+ "),e(),n(108,"div",13)(109,"strong"),t(110,"Node.js:"),e(),t(111," 18+ "),e(),n(112,"div",13)(113,"strong"),t(114,"Build:"),e(),t(115),e()()()()()),i&2&&(o(115),m(" ",c.buildDate," "))},dependencies:[d],styles:[".about[_ngcontent-%COMP%]{max-width:1000px;margin:0 auto}.tech-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:20px;margin:20px 0}.tech-item[_ngcontent-%COMP%]{padding:20px;background:#f8f9fa;border-radius:8px;text-align:center;border:1px solid #e9ecef}.tech-item[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 10px;color:#1976d2}.tech-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#666;font-size:14px}.features-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:20px;margin:20px 0}.feature-card[_ngcontent-%COMP%]{padding:20px;background:#fff;border:1px solid #e9ecef;border-radius:8px;box-shadow:0 2px 4px #0000001a;transition:transform .3s ease}.feature-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 8px #00000026}.feature-card[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 10px;color:#333;font-size:16px}.feature-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#666;font-size:14px;line-height:1.5}.architecture-info[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr;gap:30px;margin:20px 0}.arch-item[_ngcontent-%COMP%]{background:#f8f9fa;padding:20px;border-radius:8px}.arch-item[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 15px;color:#1976d2}.arch-item[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{margin:0;padding-left:20px}.arch-item[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin:8px 0;color:#555}.version-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:15px;margin:20px 0}.version-item[_ngcontent-%COMP%]{padding:15px;background:#e9ecef;border-radius:8px;text-align:center;font-size:14px}h2[_ngcontent-%COMP%]{color:#1976d2;margin-bottom:20px}h3[_ngcontent-%COMP%]{color:#333;margin:30px 0 15px;border-bottom:2px solid #1976d2;padding-bottom:5px}.features-section[_ngcontent-%COMP%], .architecture-section[_ngcontent-%COMP%], .version-info[_ngcontent-%COMP%]{margin:40px 0}@media (max-width: 768px){.architecture-info[_ngcontent-%COMP%], .features-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.tech-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fit,minmax(150px,1fr))}}"]})}};export{l as AboutComponent};
