/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    BYN: (string | undefined)[];
    GEL: (string | undefined)[];
    KZT: string[];
    PHP: (string | undefined)[];
    RON: (string | undefined)[];
    RUB: string[];
    RUR: string[];
    THB: string[];
    TMT: string[];
    TWD: string[];
    UAH: string[];
    XXX: string[];
} | undefined)[];
export default _default;
