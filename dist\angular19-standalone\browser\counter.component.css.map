{"version": 3, "sources": ["angular:styles/component:css;ef5214362c948445ca4d9841c5592780755da296688382ed13d418fbe7e05a24;D:/learning/angular/standalone/src/app/components/counter/counter.component.ts"], "sourcesContent": ["\n    .counter {\n      max-width: 600px;\n      margin: 0 auto;\n    }\n\n    .counter-display {\n      text-align: center;\n      margin: 30px 0;\n    }\n\n    .count-value {\n      font-size: 4rem;\n      font-weight: bold;\n      color: #1976d2;\n      margin-bottom: 20px;\n    }\n\n    .count-info {\n      display: flex;\n      justify-content: space-around;\n      margin: 20px 0;\n    }\n\n    .count-info p {\n      font-size: 1.2rem;\n      margin: 0;\n    }\n\n    .counter-controls {\n      display: flex;\n      justify-content: center;\n      gap: 10px;\n      margin: 30px 0;\n    }\n\n    .step-controls {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 10px;\n      margin: 20px 0;\n    }\n\n    .step-select {\n      padding: 8px;\n      border: 1px solid #ddd;\n      border-radius: 4px;\n      font-size: 14px;\n    }\n\n    .info-box {\n      background: #f8f9fa;\n      padding: 20px;\n      border-radius: 8px;\n      border-left: 4px solid #28a745;\n      margin-top: 30px;\n    }\n\n    .info-box h4 {\n      margin-top: 0;\n      color: #28a745;\n    }\n\n    h2 {\n      color: #1976d2;\n      text-align: center;\n      margin-bottom: 20px;\n    }\n  "], "mappings": ";AACI,CAAC;AACC,aAAW;AACX,UAAQ,EAAE;AACZ;AAEA,CAAC;AACC,cAAY;AACZ,UAAQ,KAAK;AACf;AAEA,CAAC;AACC,aAAW;AACX,eAAa;AACb,SAAO;AACP,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,UAAQ,KAAK;AACf;AAEA,CANC,WAMW;AACV,aAAW;AACX,UAAQ;AACV;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,OAAK;AACL,UAAQ,KAAK;AACf;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,OAAK;AACL,UAAQ,KAAK;AACf;AAEA,CAAC;AACC,WAAS;AACT,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,aAAW;AACb;AAEA,CAAC;AACC,cAAY;AACZ,WAAS;AACT,iBAAe;AACf,eAAa,IAAI,MAAM;AACvB,cAAY;AACd;AAEA,CARC,SAQS;AACR,cAAY;AACZ,SAAO;AACT;AAEA;AACE,SAAO;AACP,cAAY;AACZ,iBAAe;AACjB;", "names": []}