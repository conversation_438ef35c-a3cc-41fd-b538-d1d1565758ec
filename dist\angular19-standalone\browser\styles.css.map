{"version": 3, "sources": ["src/styles.css"], "sourcesContent": ["/* Global Styles */\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nbody {\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\n  line-height: 1.6;\n  color: #333;\n  background-color: #f5f5f5;\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n}\n\n.card {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  margin: 20px 0;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.btn {\n  background: #007bff;\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n  margin: 5px;\n}\n\n.btn:hover {\n  background: #0056b3;\n}\n\n.btn-secondary {\n  background: #6c757d;\n}\n\n.btn-secondary:hover {\n  background: #545b62;\n}\n\nnav {\n  background: #343a40;\n  padding: 1rem 0;\n}\n\nnav ul {\n  list-style: none;\n  display: flex;\n  justify-content: center;\n}\n\nnav li {\n  margin: 0 15px;\n}\n\nnav a {\n  color: white;\n  text-decoration: none;\n  padding: 10px 15px;\n  border-radius: 4px;\n  transition: background-color 0.3s;\n}\n\nnav a:hover,\nnav a.active {\n  background: #007bff;\n}\n"], "mappings": ";AACA;AACE,UAAQ;AACR,WAAS;AACT,cAAY;AACd;AAEA;AACE;AAAA,IAAa,aAAa;AAAA,IAAE,kBAAkB;AAAA,IAAE,UAAU;AAAA,IAAE,MAAM;AAAA,IAAE,SAAS;AAAA,IAAE,KAAK;AAAA,IAAE;AACtF,eAAa;AACb,SAAO;AACP,oBAAkB;AACpB;AAEA,CAAC;AACC,aAAW;AACX,UAAQ,EAAE;AACV,WAAS;AACX;AAEA,CAAC;AACC,cAAY;AACZ,iBAAe;AACf,WAAS;AACT,UAAQ,KAAK;AACb,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;AACnC;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACP,UAAQ;AACR,WAAS,KAAK;AACd,iBAAe;AACf,UAAQ;AACR,aAAW;AACX,UAAQ;AACV;AAEA,CAXC,GAWG;AACF,cAAY;AACd;AAEA,CAAC;AACC,cAAY;AACd;AAEA,CAJC,aAIa;AACZ,cAAY;AACd;AAEA;AACE,cAAY;AACZ,WAAS,KAAK;AAChB;AAEA,IAAI;AACF,cAAY;AACZ,WAAS;AACT,mBAAiB;AACnB;AAEA,IAAI;AACF,UAAQ,EAAE;AACZ;AAEA,IAAI;AACF,SAAO;AACP,mBAAiB;AACjB,WAAS,KAAK;AACd,iBAAe;AACf,cAAY,iBAAiB;AAC/B;AAEA,IAAI,CAAC;AACL,IAAI,CAAC,CAAC;AACJ,cAAY;AACd;", "names": []}