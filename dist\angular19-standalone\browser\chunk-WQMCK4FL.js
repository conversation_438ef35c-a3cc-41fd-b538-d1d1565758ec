import{c as m}from"./chunk-5YXZ7YVB.js";import{La as e,Ma as t,Ua as n,nb as r,ya as a}from"./chunk-XF3IHJ6T.js";var l=class i{static{this.\u0275fac=function(o){return new(o||i)}}static{this.\u0275cmp=a({type:i,selectors:[["app-home"]],decls:35,vars:0,consts:[[1,"home"],[1,"card"],[1,"features"],[1,"quick-links"],[1,"link-buttons"],["routerLink","/counter",1,"btn"],["routerLink","/users",1,"btn"],["routerLink","/about",1,"btn","btn-secondary"],[1,"info-box"]],template:function(o,s){o&1&&(e(0,"div",0)(1,"div",1)(2,"h2"),n(3,"Welcome to Angular 19 Standalone Components!"),t(),e(4,"p"),n(5,"This is a demonstration of Angular 19's standalone components feature."),t(),e(6,"div",2)(7,"h3"),n(8,"Features Demonstrated:"),t(),e(9,"ul")(10,"li"),n(11,"\u2705 Standalone Components (No NgModules required)"),t(),e(12,"li"),n(13,"\u2705 Lazy Loading with Standalone Components"),t(),e(14,"li"),n(15,"\u2705 Router Integration"),t(),e(16,"li"),n(17,"\u2705 Signal-based State Management"),t(),e(18,"li"),n(19,"\u2705 Modern Angular Architecture"),t()()(),e(20,"div",3)(21,"h3"),n(22,"Quick Links:"),t(),e(23,"div",4)(24,"a",5),n(25,"Try Counter Example"),t(),e(26,"a",6),n(27,"View Users List"),t(),e(28,"a",7),n(29,"About This App"),t()()(),e(30,"div",8)(31,"h4"),n(32,"What are Standalone Components?"),t(),e(33,"p"),n(34," Standalone components are a new feature in Angular that allows you to create components without the need for NgModules. They can import their own dependencies directly, making the application architecture simpler and more modular. "),t()()()())},dependencies:[r,m],styles:[".home[_ngcontent-%COMP%]{max-width:800px;margin:0 auto}.features[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{list-style:none;padding:0}.features[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{padding:8px 0;border-bottom:1px solid #eee}.quick-links[_ngcontent-%COMP%]{margin:30px 0}.link-buttons[_ngcontent-%COMP%]{display:flex;gap:10px;flex-wrap:wrap}.info-box[_ngcontent-%COMP%]{background:#f8f9fa;padding:20px;border-radius:8px;border-left:4px solid #007bff;margin-top:30px}.info-box[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin-top:0;color:#007bff}h2[_ngcontent-%COMP%]{color:#1976d2;margin-bottom:20px}h3[_ngcontent-%COMP%]{color:#333;margin:20px 0 10px}"]})}};export{l as HomeComponent};
