import {
  CommonModule,
  Component,
  computed,
  setClassMetadata,
  signal,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-VPBA6LI2.js";

// src/app/components/counter/counter.component.ts
var CounterComponent = class _CounterComponent {
  constructor() {
    this.count = signal(0);
    this.step = signal(1);
    this.doubleCount = computed(() => this.count() * 2);
    this.countStatus = computed(() => {
      const value = this.count();
      if (value === 0)
        return "Zero";
      if (value > 0)
        return "Positive";
      return "Negative";
    });
  }
  increment() {
    this.count.update((value) => value + this.step());
  }
  decrement() {
    this.count.update((value) => value - this.step());
  }
  reset() {
    this.count.set(0);
  }
  setStep(event) {
    const target = event.target;
    this.step.set(parseInt(target.value));
  }
  static {
    this.\u0275fac = function CounterComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _CounterComponent)();
    };
  }
  static {
    this.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _CounterComponent, selectors: [["app-counter"]], decls: 38, vars: 3, consts: [[1, "counter"], [1, "card"], [1, "counter-display"], [1, "count-value"], [1, "count-info"], [1, "counter-controls"], [1, "btn", 3, "click"], [1, "btn", "btn-secondary", 3, "click"], [1, "step-controls"], ["for", "step"], ["id", "step", 1, "step-select", 3, "change"], ["value", "1"], ["value", "2"], ["value", "5"], ["value", "10"], [1, "info-box"]], template: function CounterComponent_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "h2");
        \u0275\u0275text(3, "Counter Example with Signals");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(4, "p");
        \u0275\u0275text(5, "This demonstrates Angular's new signal-based reactivity system.");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(6, "div", 2)(7, "div", 3);
        \u0275\u0275text(8);
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(9, "div", 4)(10, "p");
        \u0275\u0275text(11);
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(12, "p");
        \u0275\u0275text(13);
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(14, "div", 5)(15, "button", 6);
        \u0275\u0275listener("click", function CounterComponent_Template_button_click_15_listener() {
          return ctx.increment();
        });
        \u0275\u0275text(16, " + Increment ");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(17, "button", 6);
        \u0275\u0275listener("click", function CounterComponent_Template_button_click_17_listener() {
          return ctx.decrement();
        });
        \u0275\u0275text(18, " - Decrement ");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(19, "button", 7);
        \u0275\u0275listener("click", function CounterComponent_Template_button_click_19_listener() {
          return ctx.reset();
        });
        \u0275\u0275text(20, " Reset ");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(21, "div", 8)(22, "label", 9);
        \u0275\u0275text(23, "Step Size:");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(24, "select", 10);
        \u0275\u0275listener("change", function CounterComponent_Template_select_change_24_listener($event) {
          return ctx.setStep($event);
        });
        \u0275\u0275elementStart(25, "option", 11);
        \u0275\u0275text(26, "1");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(27, "option", 12);
        \u0275\u0275text(28, "2");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(29, "option", 13);
        \u0275\u0275text(30, "5");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(31, "option", 14);
        \u0275\u0275text(32, "10");
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(33, "div", 15)(34, "h4");
        \u0275\u0275text(35, "About Signals");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(36, "p");
        \u0275\u0275text(37, " Signals provide a new way to manage reactive state in Angular. They automatically track dependencies and update the UI when values change, providing better performance and developer experience. ");
        \u0275\u0275elementEnd()()()();
      }
      if (rf & 2) {
        \u0275\u0275advance(8);
        \u0275\u0275textInterpolate(ctx.count());
        \u0275\u0275advance(3);
        \u0275\u0275textInterpolate1("Double: ", ctx.doubleCount(), "");
        \u0275\u0275advance(2);
        \u0275\u0275textInterpolate1("Status: ", ctx.countStatus(), "");
      }
    }, dependencies: [CommonModule], styles: ["\n\n.counter[_ngcontent-%COMP%] {\n  max-width: 600px;\n  margin: 0 auto;\n}\n.counter-display[_ngcontent-%COMP%] {\n  text-align: center;\n  margin: 30px 0;\n}\n.count-value[_ngcontent-%COMP%] {\n  font-size: 4rem;\n  font-weight: bold;\n  color: #1976d2;\n  margin-bottom: 20px;\n}\n.count-info[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-around;\n  margin: 20px 0;\n}\n.count-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  font-size: 1.2rem;\n  margin: 0;\n}\n.counter-controls[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  gap: 10px;\n  margin: 30px 0;\n}\n.step-controls[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 10px;\n  margin: 20px 0;\n}\n.step-select[_ngcontent-%COMP%] {\n  padding: 8px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 14px;\n}\n.info-box[_ngcontent-%COMP%] {\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 8px;\n  border-left: 4px solid #28a745;\n  margin-top: 30px;\n}\n.info-box[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  margin-top: 0;\n  color: #28a745;\n}\nh2[_ngcontent-%COMP%] {\n  color: #1976d2;\n  text-align: center;\n  margin-bottom: 20px;\n}\n/*# sourceMappingURL=counter.component.css.map */"] });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(CounterComponent, [{
    type: Component,
    args: [{ selector: "app-counter", standalone: true, imports: [CommonModule], template: `
    <div class="counter">
      <div class="card">
        <h2>Counter Example with Signals</h2>
        <p>This demonstrates Angular's new signal-based reactivity system.</p>
        
        <div class="counter-display">
          <div class="count-value">{{ count() }}</div>
          <div class="count-info">
            <p>Double: {{ doubleCount() }}</p>
            <p>Status: {{ countStatus() }}</p>
          </div>
        </div>

        <div class="counter-controls">
          <button class="btn" (click)="increment()">
            + Increment
          </button>
          <button class="btn" (click)="decrement()">
            - Decrement
          </button>
          <button class="btn btn-secondary" (click)="reset()">
            Reset
          </button>
        </div>

        <div class="step-controls">
          <label for="step">Step Size:</label>
          <select id="step" (change)="setStep($event)" class="step-select">
            <option value="1">1</option>
            <option value="2">2</option>
            <option value="5">5</option>
            <option value="10">10</option>
          </select>
        </div>

        <div class="info-box">
          <h4>About Signals</h4>
          <p>
            Signals provide a new way to manage reactive state in Angular. They automatically
            track dependencies and update the UI when values change, providing better performance
            and developer experience.
          </p>
        </div>
      </div>
    </div>
  `, styles: ["/* angular:styles/component:css;ef5214362c948445ca4d9841c5592780755da296688382ed13d418fbe7e05a24;D:/learning/angular/standalone/src/app/components/counter/counter.component.ts */\n.counter {\n  max-width: 600px;\n  margin: 0 auto;\n}\n.counter-display {\n  text-align: center;\n  margin: 30px 0;\n}\n.count-value {\n  font-size: 4rem;\n  font-weight: bold;\n  color: #1976d2;\n  margin-bottom: 20px;\n}\n.count-info {\n  display: flex;\n  justify-content: space-around;\n  margin: 20px 0;\n}\n.count-info p {\n  font-size: 1.2rem;\n  margin: 0;\n}\n.counter-controls {\n  display: flex;\n  justify-content: center;\n  gap: 10px;\n  margin: 30px 0;\n}\n.step-controls {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 10px;\n  margin: 20px 0;\n}\n.step-select {\n  padding: 8px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 14px;\n}\n.info-box {\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 8px;\n  border-left: 4px solid #28a745;\n  margin-top: 30px;\n}\n.info-box h4 {\n  margin-top: 0;\n  color: #28a745;\n}\nh2 {\n  color: #1976d2;\n  text-align: center;\n  margin-bottom: 20px;\n}\n/*# sourceMappingURL=counter.component.css.map */\n"] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(CounterComponent, { className: "CounterComponent", filePath: "src/app/components/counter/counter.component.ts", lineNumber: 126 });
})();
export {
  CounterComponent
};
//# sourceMappingURL=chunk-E4C5YVD7.js.map
