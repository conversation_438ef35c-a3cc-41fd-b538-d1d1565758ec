/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    AUD: string[];
    BRL: (string | undefined)[];
    BYN: (string | undefined)[];
    CAD: string[];
    CNY: (string | undefined)[];
    EGP: (string | undefined)[];
    GBP: string[];
    HKD: string[];
    ILS: (string | undefined)[];
    JPY: (string | undefined)[];
    KRW: (string | undefined)[];
    LBP: (string | undefined)[];
    NZD: string[];
    PHP: (string | undefined)[];
    RUR: (string | undefined)[];
    TOP: (string | undefined)[];
    TWD: (string | undefined)[];
    USD: string[];
    VND: (string | undefined)[];
    XCD: (string | undefined)[];
    XXX: never[];
} | undefined)[];
export default _default;
