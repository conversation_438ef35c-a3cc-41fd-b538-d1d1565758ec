import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    loadComponent: () => import('./components/home/<USER>').then(m => m.HomeComponent)
  },
  {
    path: 'counter',
    loadComponent: () => import('./components/counter/counter.component').then(m => m.CounterComponent)
  },
  {
    path: 'users',
    loadComponent: () => import('./components/users/users.component').then(m => m.UsersComponent)
  },
  {
    path: 'decorators',
    loadComponent: () => import('./components/decorators-examples/simple-examples.component').then(m => m.SimpleExamplesComponent)
  },
  {
    path: 'about',
    loadComponent: () => import('./components/about/about.component').then(m => m.AboutComponent)
  },
  {
    path: '**',
    redirectTo: ''
  }
];
