/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    AED: string[];
    ARS: (string | undefined)[];
    AUD: string[];
    BBD: (string | undefined)[];
    BHD: string[];
    BMD: (string | undefined)[];
    BND: (string | undefined)[];
    BSD: (string | undefined)[];
    BYN: (string | undefined)[];
    BZD: (string | undefined)[];
    CAD: string[];
    CLP: (string | undefined)[];
    CNY: string[];
    COP: (string | undefined)[];
    CUP: (string | undefined)[];
    DOP: (string | undefined)[];
    DZD: string[];
    EGP: string[];
    ERN: string[];
    FJD: (string | undefined)[];
    GBP: string[];
    GYD: (string | undefined)[];
    HKD: string[];
    IQD: string[];
    IRR: string[];
    JMD: (string | undefined)[];
    JOD: string[];
    JPY: string[];
    KWD: string[];
    KYD: (string | undefined)[];
    LBP: string[];
    LRD: (string | undefined)[];
    LYD: string[];
    MAD: string[];
    MRU: string[];
    MXN: string[];
    NZD: string[];
    OMR: string[];
    PHP: (string | undefined)[];
    QAR: string[];
    SAR: string[];
    SBD: (string | undefined)[];
    SDD: string[];
    SDG: string[];
    SRD: (string | undefined)[];
    SYP: string[];
    THB: string[];
    TND: string[];
    TTD: (string | undefined)[];
    TWD: string[];
    USD: string[];
    UYU: (string | undefined)[];
    YER: string[];
} | undefined)[];
export default _default;
