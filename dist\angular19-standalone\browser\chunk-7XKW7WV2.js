import {
  CommonModule,
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext,
  ɵɵtextInterpolate1
} from "./chunk-VPBA6LI2.js";

// src/app/components/about/about.component.ts
var AboutComponent = class _AboutComponent {
  constructor() {
    this.buildDate = (/* @__PURE__ */ new Date()).toLocaleDateString();
  }
  static {
    this.\u0275fac = function AboutComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _AboutComponent)();
    };
  }
  static {
    this.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _AboutComponent, selectors: [["app-about"]], decls: 116, vars: 1, consts: [[1, "about"], [1, "card"], [1, "tech-stack"], [1, "tech-grid"], [1, "tech-item"], [1, "features-section"], [1, "features-grid"], [1, "feature-card"], [1, "architecture-section"], [1, "architecture-info"], [1, "arch-item"], [1, "version-info"], [1, "version-grid"], [1, "version-item"]], template: function AboutComponent_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "h2");
        \u0275\u0275text(3, "About This Application");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(4, "p");
        \u0275\u0275text(5, "This is a demonstration of Angular 19's standalone components feature.");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(6, "div", 2)(7, "h3");
        \u0275\u0275text(8, "Technology Stack");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(9, "div", 3)(10, "div", 4)(11, "h4");
        \u0275\u0275text(12, "Angular 19");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(13, "p");
        \u0275\u0275text(14, "Latest version with standalone components");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(15, "div", 4)(16, "h4");
        \u0275\u0275text(17, "TypeScript");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(18, "p");
        \u0275\u0275text(19, "Type-safe JavaScript development");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(20, "div", 4)(21, "h4");
        \u0275\u0275text(22, "Signals");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(23, "p");
        \u0275\u0275text(24, "New reactive state management");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(25, "div", 4)(26, "h4");
        \u0275\u0275text(27, "Standalone Components");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(28, "p");
        \u0275\u0275text(29, "No NgModules required");
        \u0275\u0275elementEnd()()()();
        \u0275\u0275elementStart(30, "div", 5)(31, "h3");
        \u0275\u0275text(32, "Features Demonstrated");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(33, "div", 6)(34, "div", 7)(35, "h4");
        \u0275\u0275text(36, "\u{1F680} Standalone Components");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(37, "p");
        \u0275\u0275text(38, "Components that can import their own dependencies without NgModules");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(39, "div", 7)(40, "h4");
        \u0275\u0275text(41, "\u{1F4E1} Signals");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(42, "p");
        \u0275\u0275text(43, "New reactive state management system for better performance");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(44, "div", 7)(45, "h4");
        \u0275\u0275text(46, "\u{1F504} Lazy Loading");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(47, "p");
        \u0275\u0275text(48, "Route-based code splitting with standalone components");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(49, "div", 7)(50, "h4");
        \u0275\u0275text(51, "\u{1F3AF} Modern Routing");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(52, "p");
        \u0275\u0275text(53, "Simplified routing configuration with functional guards");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(54, "div", 7)(55, "h4");
        \u0275\u0275text(56, "\u{1F4F1} Responsive Design");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(57, "p");
        \u0275\u0275text(58, "Mobile-first CSS with modern layout techniques");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(59, "div", 7)(60, "h4");
        \u0275\u0275text(61, "\u26A1 Performance");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(62, "p");
        \u0275\u0275text(63, "Optimized bundle size and runtime performance");
        \u0275\u0275elementEnd()()()();
        \u0275\u0275elementStart(64, "div", 8)(65, "h3");
        \u0275\u0275text(66, "Application Architecture");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(67, "div", 9)(68, "div", 10)(69, "h4");
        \u0275\u0275text(70, "Component Structure");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(71, "ul")(72, "li");
        \u0275\u0275text(73, "App Component (Root)");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(74, "li");
        \u0275\u0275text(75, "Home Component (Landing page)");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(76, "li");
        \u0275\u0275text(77, "Counter Component (Signals demo)");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(78, "li");
        \u0275\u0275text(79, "Users Component (List management)");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(80, "li");
        \u0275\u0275text(81, "About Component (This page)");
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(82, "div", 10)(83, "h4");
        \u0275\u0275text(84, "Key Benefits");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(85, "ul")(86, "li");
        \u0275\u0275text(87, "Simplified architecture");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(88, "li");
        \u0275\u0275text(89, "Better tree-shaking");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(90, "li");
        \u0275\u0275text(91, "Reduced boilerplate");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(92, "li");
        \u0275\u0275text(93, "Improved developer experience");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(94, "li");
        \u0275\u0275text(95, "Better performance");
        \u0275\u0275elementEnd()()()()();
        \u0275\u0275elementStart(96, "div", 11)(97, "h3");
        \u0275\u0275text(98, "Version Information");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(99, "div", 12)(100, "div", 13)(101, "strong");
        \u0275\u0275text(102, "Angular:");
        \u0275\u0275elementEnd();
        \u0275\u0275text(103, " 19.x ");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(104, "div", 13)(105, "strong");
        \u0275\u0275text(106, "TypeScript:");
        \u0275\u0275elementEnd();
        \u0275\u0275text(107, " 5.6+ ");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(108, "div", 13)(109, "strong");
        \u0275\u0275text(110, "Node.js:");
        \u0275\u0275elementEnd();
        \u0275\u0275text(111, " 18+ ");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(112, "div", 13)(113, "strong");
        \u0275\u0275text(114, "Build:");
        \u0275\u0275elementEnd();
        \u0275\u0275text(115);
        \u0275\u0275elementEnd()()()()();
      }
      if (rf & 2) {
        \u0275\u0275advance(115);
        \u0275\u0275textInterpolate1(" ", ctx.buildDate, " ");
      }
    }, dependencies: [CommonModule], styles: ["\n\n.about[_ngcontent-%COMP%] {\n  max-width: 1000px;\n  margin: 0 auto;\n}\n.tech-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin: 20px 0;\n}\n.tech-item[_ngcontent-%COMP%] {\n  padding: 20px;\n  background: #f8f9fa;\n  border-radius: 8px;\n  text-align: center;\n  border: 1px solid #e9ecef;\n}\n.tech-item[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  margin: 0 0 10px 0;\n  color: #1976d2;\n}\n.tech-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0;\n  color: #666;\n  font-size: 14px;\n}\n.features-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n  margin: 20px 0;\n}\n.feature-card[_ngcontent-%COMP%] {\n  padding: 20px;\n  background: white;\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  transition: transform 0.3s ease;\n}\n.feature-card[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\n}\n.feature-card[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  margin: 0 0 10px 0;\n  color: #333;\n  font-size: 16px;\n}\n.feature-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0;\n  color: #666;\n  font-size: 14px;\n  line-height: 1.5;\n}\n.architecture-info[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 30px;\n  margin: 20px 0;\n}\n.arch-item[_ngcontent-%COMP%] {\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 8px;\n}\n.arch-item[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  margin: 0 0 15px 0;\n  color: #1976d2;\n}\n.arch-item[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\n  margin: 0;\n  padding-left: 20px;\n}\n.arch-item[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\n  margin: 8px 0;\n  color: #555;\n}\n.version-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 15px;\n  margin: 20px 0;\n}\n.version-item[_ngcontent-%COMP%] {\n  padding: 15px;\n  background: #e9ecef;\n  border-radius: 8px;\n  text-align: center;\n  font-size: 14px;\n}\nh2[_ngcontent-%COMP%] {\n  color: #1976d2;\n  margin-bottom: 20px;\n}\nh3[_ngcontent-%COMP%] {\n  color: #333;\n  margin: 30px 0 15px 0;\n  border-bottom: 2px solid #1976d2;\n  padding-bottom: 5px;\n}\n.features-section[_ngcontent-%COMP%], \n.architecture-section[_ngcontent-%COMP%], \n.version-info[_ngcontent-%COMP%] {\n  margin: 40px 0;\n}\n@media (max-width: 768px) {\n  .architecture-info[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n  .features-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n  .tech-grid[_ngcontent-%COMP%] {\n    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n  }\n}\n/*# sourceMappingURL=about.component.css.map */"] });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AboutComponent, [{
    type: Component,
    args: [{ selector: "app-about", standalone: true, imports: [CommonModule], template: `
    <div class="about">
      <div class="card">
        <h2>About This Application</h2>
        <p>This is a demonstration of Angular 19's standalone components feature.</p>

        <div class="tech-stack">
          <h3>Technology Stack</h3>
          <div class="tech-grid">
            <div class="tech-item">
              <h4>Angular 19</h4>
              <p>Latest version with standalone components</p>
            </div>
            <div class="tech-item">
              <h4>TypeScript</h4>
              <p>Type-safe JavaScript development</p>
            </div>
            <div class="tech-item">
              <h4>Signals</h4>
              <p>New reactive state management</p>
            </div>
            <div class="tech-item">
              <h4>Standalone Components</h4>
              <p>No NgModules required</p>
            </div>
          </div>
        </div>

        <div class="features-section">
          <h3>Features Demonstrated</h3>
          <div class="features-grid">
            <div class="feature-card">
              <h4>\u{1F680} Standalone Components</h4>
              <p>Components that can import their own dependencies without NgModules</p>
            </div>
            <div class="feature-card">
              <h4>\u{1F4E1} Signals</h4>
              <p>New reactive state management system for better performance</p>
            </div>
            <div class="feature-card">
              <h4>\u{1F504} Lazy Loading</h4>
              <p>Route-based code splitting with standalone components</p>
            </div>
            <div class="feature-card">
              <h4>\u{1F3AF} Modern Routing</h4>
              <p>Simplified routing configuration with functional guards</p>
            </div>
            <div class="feature-card">
              <h4>\u{1F4F1} Responsive Design</h4>
              <p>Mobile-first CSS with modern layout techniques</p>
            </div>
            <div class="feature-card">
              <h4>\u26A1 Performance</h4>
              <p>Optimized bundle size and runtime performance</p>
            </div>
          </div>
        </div>

        <div class="architecture-section">
          <h3>Application Architecture</h3>
          <div class="architecture-info">
            <div class="arch-item">
              <h4>Component Structure</h4>
              <ul>
                <li>App Component (Root)</li>
                <li>Home Component (Landing page)</li>
                <li>Counter Component (Signals demo)</li>
                <li>Users Component (List management)</li>
                <li>About Component (This page)</li>
              </ul>
            </div>
            <div class="arch-item">
              <h4>Key Benefits</h4>
              <ul>
                <li>Simplified architecture</li>
                <li>Better tree-shaking</li>
                <li>Reduced boilerplate</li>
                <li>Improved developer experience</li>
                <li>Better performance</li>
              </ul>
            </div>
          </div>
        </div>

        <div class="version-info">
          <h3>Version Information</h3>
          <div class="version-grid">
            <div class="version-item">
              <strong>Angular:</strong> 19.x
            </div>
            <div class="version-item">
              <strong>TypeScript:</strong> 5.6+
            </div>
            <div class="version-item">
              <strong>Node.js:</strong> 18+
            </div>
            <div class="version-item">
              <strong>Build:</strong> {{ buildDate }}
            </div>
          </div>
        </div>
      </div>
    </div>
  `, styles: ["/* angular:styles/component:css;807c68d7596a469b9f7583c62f636b97e4375dcb5b56425d092dcfbea1142c31;D:/learning/angular/standalone/src/app/components/about/about.component.ts */\n.about {\n  max-width: 1000px;\n  margin: 0 auto;\n}\n.tech-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin: 20px 0;\n}\n.tech-item {\n  padding: 20px;\n  background: #f8f9fa;\n  border-radius: 8px;\n  text-align: center;\n  border: 1px solid #e9ecef;\n}\n.tech-item h4 {\n  margin: 0 0 10px 0;\n  color: #1976d2;\n}\n.tech-item p {\n  margin: 0;\n  color: #666;\n  font-size: 14px;\n}\n.features-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n  margin: 20px 0;\n}\n.feature-card {\n  padding: 20px;\n  background: white;\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  transition: transform 0.3s ease;\n}\n.feature-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\n}\n.feature-card h4 {\n  margin: 0 0 10px 0;\n  color: #333;\n  font-size: 16px;\n}\n.feature-card p {\n  margin: 0;\n  color: #666;\n  font-size: 14px;\n  line-height: 1.5;\n}\n.architecture-info {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 30px;\n  margin: 20px 0;\n}\n.arch-item {\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 8px;\n}\n.arch-item h4 {\n  margin: 0 0 15px 0;\n  color: #1976d2;\n}\n.arch-item ul {\n  margin: 0;\n  padding-left: 20px;\n}\n.arch-item li {\n  margin: 8px 0;\n  color: #555;\n}\n.version-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 15px;\n  margin: 20px 0;\n}\n.version-item {\n  padding: 15px;\n  background: #e9ecef;\n  border-radius: 8px;\n  text-align: center;\n  font-size: 14px;\n}\nh2 {\n  color: #1976d2;\n  margin-bottom: 20px;\n}\nh3 {\n  color: #333;\n  margin: 30px 0 15px 0;\n  border-bottom: 2px solid #1976d2;\n  padding-bottom: 5px;\n}\n.features-section,\n.architecture-section,\n.version-info {\n  margin: 40px 0;\n}\n@media (max-width: 768px) {\n  .architecture-info {\n    grid-template-columns: 1fr;\n  }\n  .features-grid {\n    grid-template-columns: 1fr;\n  }\n  .tech-grid {\n    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n  }\n}\n/*# sourceMappingURL=about.component.css.map */\n"] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(AboutComponent, { className: "AboutComponent", filePath: "src/app/components/about/about.component.ts", lineNumber: 254 });
})();
export {
  AboutComponent
};
//# sourceMappingURL=chunk-7XKW7WV2.js.map
