import{Aa as p,Ba as v,Ca as fe,Ea as pe,Ia as J,Ja as S,K as E,Ka as ge,<PERSON> as s,M as z,Ma as a,O as b,Oa as me,Pa as _,Qa as _e,Ua as l,Va as X,W as ue,Wa as y,X as de,Xa as ve,Y as ce,Ya as ye,Z as R,Za as Ce,_a as x,a as c,b as f,bb as Ve,ca as U,db as be,eb as C,fb as V,g as oe,ga as w,ha as m,hb as Y,j as se,mb as De,nb as Me,o as ae,qa as h,ta as F,u as le,ua as u,ya as he,za as Z}from"./chunk-XF3IHJ6T.js";var Ne=(()=>{class n{_renderer;_elementRef;onChange=t=>{};onTouched=()=>{};constructor(t,i){this._renderer=t,this._elementRef=i}setProperty(t,i){this._renderer.setProperty(this._elementRef.nativeElement,t,i)}registerOnTouched(t){this.onTouched=t}registerOnChange(t){this.onChange=t}setDisabledState(t){this.setProperty("disabled",t)}static \u0275fac=function(i){return new(i||n)(u(F),u(w))};static \u0275dir=p({type:n})}return n})(),ne=(()=>{class n extends Ne{static \u0275fac=(()=>{let t;return function(r){return(t||(t=R(n)))(r||n)}})();static \u0275dir=p({type:n,features:[v]})}return n})(),L=new b("");var Ke={provide:L,useExisting:E(()=>W),multi:!0};function Qe(){let n=Y()?Y().getUserAgent():"";return/android (\d+)/.test(n.toLowerCase())}var et=new b(""),W=(()=>{class n extends Ne{_compositionMode;_composing=!1;constructor(t,i,r){super(t,i),this._compositionMode=r,this._compositionMode==null&&(this._compositionMode=!Qe())}writeValue(t){let i=t??"";this.setProperty("value",i)}_handleInput(t){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(t)}_compositionStart(){this._composing=!0}_compositionEnd(t){this._composing=!1,this._compositionMode&&this.onChange(t)}static \u0275fac=function(i){return new(i||n)(u(F),u(w),u(et,8))};static \u0275dir=p({type:n,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(i,r){i&1&&_("input",function(d){return r._handleInput(d.target.value)})("blur",function(){return r.onTouched()})("compositionstart",function(){return r._compositionStart()})("compositionend",function(d){return r._compositionEnd(d.target.value)})},standalone:!1,features:[x([Ke]),v]})}return n})();var tt=new b(""),nt=new b("");function Pe(n){return n!=null}function ke(n){return pe(n)?se(n):n}function Te(n){let e={};return n.forEach(t=>{e=t!=null?c(c({},e),t):e}),Object.keys(e).length===0?null:e}function Re(n,e){return e.map(t=>t(n))}function it(n){return!n.validate}function Ue(n){return n.map(e=>it(e)?e:t=>e.validate(t))}function rt(n){if(!n)return null;let e=n.filter(Pe);return e.length==0?null:function(t){return Te(Re(t,e))}}function je(n){return n!=null?rt(Ue(n)):null}function ot(n){if(!n)return null;let e=n.filter(Pe);return e.length==0?null:function(t){let i=Re(t,e).map(ke);return le(i).pipe(ae(Te))}}function Ge(n){return n!=null?ot(Ue(n)):null}function Ae(n,e){return n===null?[e]:Array.isArray(n)?[...n,e]:[n,e]}function st(n){return n._rawValidators}function at(n){return n._rawAsyncValidators}function K(n){return n?Array.isArray(n)?n:[n]:[]}function G(n,e){return Array.isArray(n)?n.includes(e):n===e}function Ee(n,e){let t=K(e);return K(n).forEach(r=>{G(t,r)||t.push(r)}),t}function we(n,e){return K(e).filter(t=>!G(n,t))}var B=class{get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators=[];_rawAsyncValidators=[];_setValidators(e){this._rawValidators=e||[],this._composedValidatorFn=je(this._rawValidators)}_setAsyncValidators(e){this._rawAsyncValidators=e||[],this._composedAsyncValidatorFn=Ge(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_onDestroyCallbacks=[];_registerOnDestroy(e){this._onDestroyCallbacks.push(e)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(e=>e()),this._onDestroyCallbacks=[]}reset(e=void 0){this.control&&this.control.reset(e)}hasError(e,t){return this.control?this.control.hasError(e,t):!1}getError(e,t){return this.control?this.control.getError(e,t):null}},Q=class extends B{name;get formDirective(){return null}get path(){return null}},k=class extends B{_parent=null;name=null;valueAccessor=null},ee=class{_cd;constructor(e){this._cd=e}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},lt={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},Qt=f(c({},lt),{"[class.ng-submitted]":"isSubmitted"}),Be=(()=>{class n extends ee{constructor(t){super(t)}static \u0275fac=function(i){return new(i||n)(u(k,2))};static \u0275dir=p({type:n,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(i,r){i&2&&S("ng-untouched",r.isUntouched)("ng-touched",r.isTouched)("ng-pristine",r.isPristine)("ng-dirty",r.isDirty)("ng-valid",r.isValid)("ng-invalid",r.isInvalid)("ng-pending",r.isPending)},standalone:!1,features:[v]})}return n})();var I="VALID",j="INVALID",D="PENDING",O="DISABLED",A=class{},H=class extends A{value;source;constructor(e,t){super(),this.value=e,this.source=t}},N=class extends A{pristine;source;constructor(e,t){super(),this.pristine=e,this.source=t}},P=class extends A{touched;source;constructor(e,t){super(),this.touched=e,this.source=t}},M=class extends A{status;source;constructor(e,t){super(),this.status=e,this.source=t}};function ut(n){return($(n)?n.validators:n)||null}function dt(n){return Array.isArray(n)?je(n):n||null}function ct(n,e){return($(e)?e.asyncValidators:n)||null}function ht(n){return Array.isArray(n)?Ge(n):n||null}function $(n){return n!=null&&!Array.isArray(n)&&typeof n=="object"}var te=class{_pendingDirty=!1;_hasOwnPendingAsyncValidator=null;_pendingTouched=!1;_onCollectionChange=()=>{};_updateOn;_parent=null;_asyncValidationSubscription;_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators;_rawAsyncValidators;value;constructor(e,t){this._assignValidators(e),this._assignAsyncValidators(t)}get validator(){return this._composedValidatorFn}set validator(e){this._rawValidators=this._composedValidatorFn=e}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(e){this._rawAsyncValidators=this._composedAsyncValidatorFn=e}get parent(){return this._parent}get status(){return C(this.statusReactive)}set status(e){C(()=>this.statusReactive.set(e))}_status=V(()=>this.statusReactive());statusReactive=m(void 0);get valid(){return this.status===I}get invalid(){return this.status===j}get pending(){return this.status==D}get disabled(){return this.status===O}get enabled(){return this.status!==O}errors;get pristine(){return C(this.pristineReactive)}set pristine(e){C(()=>this.pristineReactive.set(e))}_pristine=V(()=>this.pristineReactive());pristineReactive=m(!0);get dirty(){return!this.pristine}get touched(){return C(this.touchedReactive)}set touched(e){C(()=>this.touchedReactive.set(e))}_touched=V(()=>this.touchedReactive());touchedReactive=m(!1);get untouched(){return!this.touched}_events=new oe;events=this._events.asObservable();valueChanges;statusChanges;get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(e){this._assignValidators(e)}setAsyncValidators(e){this._assignAsyncValidators(e)}addValidators(e){this.setValidators(Ee(e,this._rawValidators))}addAsyncValidators(e){this.setAsyncValidators(Ee(e,this._rawAsyncValidators))}removeValidators(e){this.setValidators(we(e,this._rawValidators))}removeAsyncValidators(e){this.setAsyncValidators(we(e,this._rawAsyncValidators))}hasValidator(e){return G(this._rawValidators,e)}hasAsyncValidator(e){return G(this._rawAsyncValidators,e)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(e={}){let t=this.touched===!1;this.touched=!0;let i=e.sourceControl??this;this._parent&&!e.onlySelf&&this._parent.markAsTouched(f(c({},e),{sourceControl:i})),t&&e.emitEvent!==!1&&this._events.next(new P(!0,i))}markAllAsTouched(e={}){this.markAsTouched({onlySelf:!0,emitEvent:e.emitEvent,sourceControl:this}),this._forEachChild(t=>t.markAllAsTouched(e))}markAsUntouched(e={}){let t=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let i=e.sourceControl??this;this._forEachChild(r=>{r.markAsUntouched({onlySelf:!0,emitEvent:e.emitEvent,sourceControl:i})}),this._parent&&!e.onlySelf&&this._parent._updateTouched(e,i),t&&e.emitEvent!==!1&&this._events.next(new P(!1,i))}markAsDirty(e={}){let t=this.pristine===!0;this.pristine=!1;let i=e.sourceControl??this;this._parent&&!e.onlySelf&&this._parent.markAsDirty(f(c({},e),{sourceControl:i})),t&&e.emitEvent!==!1&&this._events.next(new N(!1,i))}markAsPristine(e={}){let t=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let i=e.sourceControl??this;this._forEachChild(r=>{r.markAsPristine({onlySelf:!0,emitEvent:e.emitEvent})}),this._parent&&!e.onlySelf&&this._parent._updatePristine(e,i),t&&e.emitEvent!==!1&&this._events.next(new N(!0,i))}markAsPending(e={}){this.status=D;let t=e.sourceControl??this;e.emitEvent!==!1&&(this._events.next(new M(this.status,t)),this.statusChanges.emit(this.status)),this._parent&&!e.onlySelf&&this._parent.markAsPending(f(c({},e),{sourceControl:t}))}disable(e={}){let t=this._parentMarkedDirty(e.onlySelf);this.status=O,this.errors=null,this._forEachChild(r=>{r.disable(f(c({},e),{onlySelf:!0}))}),this._updateValue();let i=e.sourceControl??this;e.emitEvent!==!1&&(this._events.next(new H(this.value,i)),this._events.next(new M(this.status,i)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(f(c({},e),{skipPristineCheck:t}),this),this._onDisabledChange.forEach(r=>r(!0))}enable(e={}){let t=this._parentMarkedDirty(e.onlySelf);this.status=I,this._forEachChild(i=>{i.enable(f(c({},e),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:e.emitEvent}),this._updateAncestors(f(c({},e),{skipPristineCheck:t}),this),this._onDisabledChange.forEach(i=>i(!1))}_updateAncestors(e,t){this._parent&&!e.onlySelf&&(this._parent.updateValueAndValidity(e),e.skipPristineCheck||this._parent._updatePristine({},t),this._parent._updateTouched({},t))}setParent(e){this._parent=e}getRawValue(){return this.value}updateValueAndValidity(e={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let i=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===I||this.status===D)&&this._runAsyncValidator(i,e.emitEvent)}let t=e.sourceControl??this;e.emitEvent!==!1&&(this._events.next(new H(this.value,t)),this._events.next(new M(this.status,t)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!e.onlySelf&&this._parent.updateValueAndValidity(f(c({},e),{sourceControl:t}))}_updateTreeValidity(e={emitEvent:!0}){this._forEachChild(t=>t._updateTreeValidity(e)),this.updateValueAndValidity({onlySelf:!0,emitEvent:e.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?O:I}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(e,t){if(this.asyncValidator){this.status=D,this._hasOwnPendingAsyncValidator={emitEvent:t!==!1};let i=ke(this.asyncValidator(this));this._asyncValidationSubscription=i.subscribe(r=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(r,{emitEvent:t,shouldHaveEmitted:e})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let e=this._hasOwnPendingAsyncValidator?.emitEvent??!1;return this._hasOwnPendingAsyncValidator=null,e}return!1}setErrors(e,t={}){this.errors=e,this._updateControlsErrors(t.emitEvent!==!1,this,t.shouldHaveEmitted)}get(e){let t=e;return t==null||(Array.isArray(t)||(t=t.split(".")),t.length===0)?null:t.reduce((i,r)=>i&&i._find(r),this)}getError(e,t){let i=t?this.get(t):this;return i&&i.errors?i.errors[e]:null}hasError(e,t){return!!this.getError(e,t)}get root(){let e=this;for(;e._parent;)e=e._parent;return e}_updateControlsErrors(e,t,i){this.status=this._calculateStatus(),e&&this.statusChanges.emit(this.status),(e||i)&&this._events.next(new M(this.status,t)),this._parent&&this._parent._updateControlsErrors(e,t,i)}_initObservables(){this.valueChanges=new U,this.statusChanges=new U}_calculateStatus(){return this._allControlsDisabled()?O:this.errors?j:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(D)?D:this._anyControlsHaveStatus(j)?j:I}_anyControlsHaveStatus(e){return this._anyControls(t=>t.status===e)}_anyControlsDirty(){return this._anyControls(e=>e.dirty)}_anyControlsTouched(){return this._anyControls(e=>e.touched)}_updatePristine(e,t){let i=!this._anyControlsDirty(),r=this.pristine!==i;this.pristine=i,this._parent&&!e.onlySelf&&this._parent._updatePristine(e,t),r&&this._events.next(new N(this.pristine,t))}_updateTouched(e={},t){this.touched=this._anyControlsTouched(),this._events.next(new P(this.touched,t)),this._parent&&!e.onlySelf&&this._parent._updateTouched(e,t)}_onDisabledChange=[];_registerOnCollectionChange(e){this._onCollectionChange=e}_setUpdateStrategy(e){$(e)&&e.updateOn!=null&&(this._updateOn=e.updateOn)}_parentMarkedDirty(e){let t=this._parent&&this._parent.dirty;return!e&&!!t&&!this._parent._anyControlsDirty()}_find(e){return null}_assignValidators(e){this._rawValidators=Array.isArray(e)?e.slice():e,this._composedValidatorFn=dt(this._rawValidators)}_assignAsyncValidators(e){this._rawAsyncValidators=Array.isArray(e)?e.slice():e,this._composedAsyncValidatorFn=ht(this._rawAsyncValidators)}};var He=new b("",{providedIn:"root",factory:()=>ie}),ie="always";function ft(n,e){return[...e.path,n]}function pt(n,e,t=ie){mt(n,e),e.valueAccessor.writeValue(n.value),(n.disabled||t==="always")&&e.valueAccessor.setDisabledState?.(n.disabled),_t(n,e),yt(n,e),vt(n,e),gt(n,e)}function Fe(n,e){n.forEach(t=>{t.registerOnValidatorChange&&t.registerOnValidatorChange(e)})}function gt(n,e){if(e.valueAccessor.setDisabledState){let t=i=>{e.valueAccessor.setDisabledState(i)};n.registerOnDisabledChange(t),e._registerOnDestroy(()=>{n._unregisterOnDisabledChange(t)})}}function mt(n,e){let t=st(n);e.validator!==null?n.setValidators(Ae(t,e.validator)):typeof t=="function"&&n.setValidators([t]);let i=at(n);e.asyncValidator!==null?n.setAsyncValidators(Ae(i,e.asyncValidator)):typeof i=="function"&&n.setAsyncValidators([i]);let r=()=>n.updateValueAndValidity();Fe(e._rawValidators,r),Fe(e._rawAsyncValidators,r)}function _t(n,e){e.valueAccessor.registerOnChange(t=>{n._pendingValue=t,n._pendingChange=!0,n._pendingDirty=!0,n.updateOn==="change"&&Le(n,e)})}function vt(n,e){e.valueAccessor.registerOnTouched(()=>{n._pendingTouched=!0,n.updateOn==="blur"&&n._pendingChange&&Le(n,e),n.updateOn!=="submit"&&n.markAsTouched()})}function Le(n,e){n._pendingDirty&&n.markAsDirty(),n.setValue(n._pendingValue,{emitModelToViewChange:!1}),e.viewToModelUpdate(n._pendingValue),n._pendingChange=!1}function yt(n,e){let t=(i,r)=>{e.valueAccessor.writeValue(i),r&&e.viewToModelUpdate(i)};n.registerOnChange(t),e._registerOnDestroy(()=>{n._unregisterOnChange(t)})}function Ct(n,e){if(!n.hasOwnProperty("model"))return!1;let t=n.model;return t.isFirstChange()?!0:!Object.is(e,t.currentValue)}function Vt(n){return Object.getPrototypeOf(n.constructor)===ne}function bt(n,e){if(!e)return null;Array.isArray(e);let t,i,r;return e.forEach(o=>{o.constructor===W?t=o:Vt(o)?i=o:r=o}),r||i||t||null}function Se(n,e){let t=n.indexOf(e);t>-1&&n.splice(t,1)}function xe(n){return typeof n=="object"&&n!==null&&Object.keys(n).length===2&&"value"in n&&"disabled"in n}var Dt=class extends te{defaultValue=null;_onChange=[];_pendingValue;_pendingChange=!1;constructor(e=null,t,i){super(ut(t),ct(i,t)),this._applyFormState(e),this._setUpdateStrategy(t),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),$(t)&&(t.nonNullable||t.initialValueIsDefault)&&(xe(e)?this.defaultValue=e.value:this.defaultValue=e)}setValue(e,t={}){this.value=this._pendingValue=e,this._onChange.length&&t.emitModelToViewChange!==!1&&this._onChange.forEach(i=>i(this.value,t.emitViewToModelChange!==!1)),this.updateValueAndValidity(t)}patchValue(e,t={}){this.setValue(e,t)}reset(e=this.defaultValue,t={}){this._applyFormState(e),this.markAsPristine(t),this.markAsUntouched(t),this.setValue(this.value,t),this._pendingChange=!1}_updateValue(){}_anyControls(e){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(e){this._onChange.push(e)}_unregisterOnChange(e){Se(this._onChange,e)}registerOnDisabledChange(e){this._onDisabledChange.push(e)}_unregisterOnDisabledChange(e){Se(this._onDisabledChange,e)}_forEachChild(e){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(e){xe(e)?(this.value=this._pendingValue=e.value,e.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=e}};var Mt={provide:k,useExisting:E(()=>re)},Ie=Promise.resolve(),re=(()=>{class n extends k{_changeDetectorRef;callSetDisabledState;control=new Dt;static ngAcceptInputType_isDisabled;_registered=!1;viewModel;name="";isDisabled;model;options;update=new U;constructor(t,i,r,o,d,g){super(),this._changeDetectorRef=d,this.callSetDisabledState=g,this._parent=t,this._setValidators(i),this._setAsyncValidators(r),this.valueAccessor=bt(this,o)}ngOnChanges(t){if(this._checkForErrors(),!this._registered||"name"in t){if(this._registered&&(this._checkName(),this.formDirective)){let i=t.name.previousValue;this.formDirective.removeControl({name:i,path:this._getPath(i)})}this._setUpControl()}"isDisabled"in t&&this._updateDisabled(t),Ct(t,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(t){this.viewModel=t,this.update.emit(t)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!!(this.options&&this.options.standalone)}_setUpStandalone(){pt(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._checkName()}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),!this._isStandalone()&&this.name}_updateValue(t){Ie.then(()=>{this.control.setValue(t,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(t){let i=t.isDisabled.currentValue,r=i!==0&&be(i);Ie.then(()=>{r&&!this.control.disabled?this.control.disable():!r&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(t){return this._parent?ft(t,this._parent):[t]}static \u0275fac=function(i){return new(i||n)(u(Q,9),u(tt,10),u(nt,10),u(L,10),u(Ve,8),u(He,8))};static \u0275dir=p({type:n,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"],options:[0,"ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],standalone:!1,features:[x([Mt]),v,ue]})}return n})();var At={provide:L,useExisting:E(()=>$e),multi:!0};function We(n,e){return n==null?`${e}`:(e&&typeof e=="object"&&(e="Object"),`${n}: ${e}`.slice(0,50))}function Et(n){return n.split(":")[0]}var $e=(()=>{class n extends ne{value;_optionMap=new Map;_idCounter=0;set compareWith(t){this._compareWith=t}_compareWith=Object.is;writeValue(t){this.value=t;let i=this._getOptionId(t),r=We(i,t);this.setProperty("value",r)}registerOnChange(t){this.onChange=i=>{this.value=this._getOptionValue(i),t(this.value)}}_registerOption(){return(this._idCounter++).toString()}_getOptionId(t){for(let i of this._optionMap.keys())if(this._compareWith(this._optionMap.get(i),t))return i;return null}_getOptionValue(t){let i=Et(t);return this._optionMap.has(i)?this._optionMap.get(i):t}static \u0275fac=(()=>{let t;return function(r){return(t||(t=R(n)))(r||n)}})();static \u0275dir=p({type:n,selectors:[["select","formControlName","",3,"multiple",""],["select","formControl","",3,"multiple",""],["select","ngModel","",3,"multiple",""]],hostBindings:function(i,r){i&1&&_("change",function(d){return r.onChange(d.target.value)})("blur",function(){return r.onTouched()})},inputs:{compareWith:"compareWith"},standalone:!1,features:[x([At]),v]})}return n})(),qe=(()=>{class n{_element;_renderer;_select;id;constructor(t,i,r){this._element=t,this._renderer=i,this._select=r,this._select&&(this.id=this._select._registerOption())}set ngValue(t){this._select!=null&&(this._select._optionMap.set(this.id,t),this._setElementValue(We(this.id,t)),this._select.writeValue(this._select.value))}set value(t){this._setElementValue(t),this._select&&this._select.writeValue(this._select.value)}_setElementValue(t){this._renderer.setProperty(this._element.nativeElement,"value",t)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}static \u0275fac=function(i){return new(i||n)(u(w),u(F),u($e,9))};static \u0275dir=p({type:n,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"},standalone:!1})}return n})(),wt={provide:L,useExisting:E(()=>ze),multi:!0};function Oe(n,e){return n==null?`${e}`:(typeof e=="string"&&(e=`'${e}'`),e&&typeof e=="object"&&(e="Object"),`${n}: ${e}`.slice(0,50))}function Ft(n){return n.split(":")[0]}var ze=(()=>{class n extends ne{value;_optionMap=new Map;_idCounter=0;set compareWith(t){this._compareWith=t}_compareWith=Object.is;writeValue(t){this.value=t;let i;if(Array.isArray(t)){let r=t.map(o=>this._getOptionId(o));i=(o,d)=>{o._setSelected(r.indexOf(d.toString())>-1)}}else i=(r,o)=>{r._setSelected(!1)};this._optionMap.forEach(i)}registerOnChange(t){this.onChange=i=>{let r=[],o=i.selectedOptions;if(o!==void 0){let d=o;for(let g=0;g<d.length;g++){let T=d[g],q=this._getOptionValue(T.value);r.push(q)}}else{let d=i.options;for(let g=0;g<d.length;g++){let T=d[g];if(T.selected){let q=this._getOptionValue(T.value);r.push(q)}}}this.value=r,t(r)}}_registerOption(t){let i=(this._idCounter++).toString();return this._optionMap.set(i,t),i}_getOptionId(t){for(let i of this._optionMap.keys())if(this._compareWith(this._optionMap.get(i)._value,t))return i;return null}_getOptionValue(t){let i=Ft(t);return this._optionMap.has(i)?this._optionMap.get(i)._value:t}static \u0275fac=(()=>{let t;return function(r){return(t||(t=R(n)))(r||n)}})();static \u0275dir=p({type:n,selectors:[["select","multiple","","formControlName",""],["select","multiple","","formControl",""],["select","multiple","","ngModel",""]],hostBindings:function(i,r){i&1&&_("change",function(d){return r.onChange(d.target)})("blur",function(){return r.onTouched()})},inputs:{compareWith:"compareWith"},standalone:!1,features:[x([wt]),v]})}return n})(),Ze=(()=>{class n{_element;_renderer;_select;id;_value;constructor(t,i,r){this._element=t,this._renderer=i,this._select=r,this._select&&(this.id=this._select._registerOption(this))}set ngValue(t){this._select!=null&&(this._value=t,this._setElementValue(Oe(this.id,t)),this._select.writeValue(this._select.value))}set value(t){this._select?(this._value=t,this._setElementValue(Oe(this.id,t)),this._select.writeValue(this._select.value)):this._setElementValue(t)}_setElementValue(t){this._renderer.setProperty(this._element.nativeElement,"value",t)}_setSelected(t){this._renderer.setProperty(this._element.nativeElement,"selected",t)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}static \u0275fac=function(i){return new(i||n)(u(w),u(F),u(ze,9))};static \u0275dir=p({type:n,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"},standalone:!1})}return n})();var St=(()=>{class n{static \u0275fac=function(i){return new(i||n)};static \u0275mod=Z({type:n});static \u0275inj=z({})}return n})();var Je=(()=>{class n{static withConfig(t){return{ngModule:n,providers:[{provide:He,useValue:t.callSetDisabledState??ie}]}}static \u0275fac=function(i){return new(i||n)};static \u0275mod=Z({type:n});static \u0275inj=z({imports:[St]})}return n})();function It(n,e){if(n&1){let t=me();s(0,"div",19)(1,"div",20)(2,"h4"),l(3),a(),s(4,"p"),l(5),a(),s(6,"span",21),l(7),a(),s(8,"span",22),l(9),a()(),s(10,"div",23)(11,"button",24),_("click",function(){let r=de(t).$implicit,o=_e();return ce(o.toggleUserStatus(r.id))}),l(12),a()()()}if(n&2){let t=e.$implicit;S("inactive",!t.active),h(3),X(t.name),h(2),X(t.email),h(),ge("role-"+t.role.toLowerCase()),h(),y(" ",t.role," "),h(),S("active",t.active),h(),y(" ",t.active?"Active":"Inactive"," "),h(3),y(" ",t.active?"Deactivate":"Activate"," ")}}var Xe=class n{constructor(){this.searchTerm="";this.users=m([{id:1,name:"John Doe",email:"<EMAIL>",role:"Admin",active:!0},{id:2,name:"Jane Smith",email:"<EMAIL>",role:"User",active:!0},{id:3,name:"Bob Johnson",email:"<EMAIL>",role:"Manager",active:!1},{id:4,name:"Alice Brown",email:"<EMAIL>",role:"User",active:!0},{id:5,name:"Charlie Wilson",email:"<EMAIL>",role:"Admin",active:!0},{id:6,name:"Diana Davis",email:"<EMAIL>",role:"Manager",active:!1}]);this.searchTermSignal=m("");this.roleFilter=m("");this.showActiveOnly=m(!1);this.filteredUsers=V(()=>{let e=this.users(),t=this.searchTermSignal().toLowerCase();t&&(e=e.filter(r=>r.name.toLowerCase().includes(t)||r.email.toLowerCase().includes(t)));let i=this.roleFilter();return i&&(e=e.filter(r=>r.role===i)),this.showActiveOnly()&&(e=e.filter(r=>r.active)),e});this.activeUsersCount=V(()=>this.users().filter(e=>e.active).length)}updateSearchTerm(e){let t=e.target;this.searchTermSignal.set(t.value)}updateRoleFilter(e){let t=e.target;this.roleFilter.set(t.value)}toggleActiveFilter(){this.showActiveOnly.update(e=>!e)}toggleUserStatus(e){this.users.update(t=>t.map(i=>i.id===e?f(c({},i),{active:!i.active}):i))}static{this.\u0275fac=function(t){return new(t||n)}}static{this.\u0275cmp=he({type:n,selectors:[["app-users"]],decls:50,vars:6,consts:[[1,"users"],[1,"card"],[1,"controls"],[1,"search-box"],["for","search"],["id","search","type","text","placeholder","Search by name or email...",1,"search-input",3,"ngModelChange","input","ngModel"],[1,"filter-box"],["for","roleFilter"],["id","roleFilter",1,"filter-select",3,"change"],["value",""],["value","Admin"],["value","User"],["value","Manager"],[1,"status-filter"],["type","checkbox",3,"change","checked"],[1,"stats"],[1,"users-list"],["class","user-card",3,"inactive",4,"ngFor","ngForOf"],[1,"info-box"],[1,"user-card"],[1,"user-info"],[1,"role-badge"],[1,"status-badge"],[1,"user-actions"],[1,"btn","btn-sm",3,"click"]],template:function(t,i){t&1&&(s(0,"div",0)(1,"div",1)(2,"h2"),l(3,"Users Management"),a(),s(4,"p"),l(5,"This demonstrates list management with signals and filtering."),a(),s(6,"div",2)(7,"div",3)(8,"label",4),l(9,"Search Users:"),a(),s(10,"input",5),Ce("ngModelChange",function(o){return ye(i.searchTerm,o)||(i.searchTerm=o),o}),_("input",function(o){return i.updateSearchTerm(o)}),a()(),s(11,"div",6)(12,"label",7),l(13,"Filter by Role:"),a(),s(14,"select",8),_("change",function(o){return i.updateRoleFilter(o)}),s(15,"option",9),l(16,"All Roles"),a(),s(17,"option",10),l(18,"Admin"),a(),s(19,"option",11),l(20,"User"),a(),s(21,"option",12),l(22,"Manager"),a()()(),s(23,"div",13)(24,"label")(25,"input",14),_("change",function(){return i.toggleActiveFilter()}),a(),l(26," Show Active Only "),a()()(),s(27,"div",15)(28,"p"),l(29),a(),s(30,"p"),l(31),a(),s(32,"p"),l(33),a()(),s(34,"div",16),fe(35,It,13,11,"div",17),a(),s(36,"div",18)(37,"h4"),l(38,"Features Demonstrated"),a(),s(39,"ul")(40,"li"),l(41,"Signal-based state management"),a(),s(42,"li"),l(43,"Computed signals for filtering and statistics"),a(),s(44,"li"),l(45,"Two-way data binding with FormsModule"),a(),s(46,"li"),l(47,"Dynamic CSS classes"),a(),s(48,"li"),l(49,"List manipulation with signals"),a()()()()()),t&2&&(h(10),ve("ngModel",i.searchTerm),h(15),J("checked",i.showActiveOnly()),h(4),y("Total Users: ",i.users().length,""),h(2),y("Filtered Results: ",i.filteredUsers().length,""),h(2),y("Active Users: ",i.activeUsersCount(),""),h(2),J("ngForOf",i.filteredUsers()))},dependencies:[Me,De,Je,qe,Ze,W,Be,re],styles:[".users[_ngcontent-%COMP%]{max-width:900px;margin:0 auto}.controls[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr 1fr;gap:20px;margin:20px 0;padding:20px;background:#f8f9fa;border-radius:8px}.search-input[_ngcontent-%COMP%], .filter-select[_ngcontent-%COMP%]{width:100%;padding:8px;border:1px solid #ddd;border-radius:4px;font-size:14px}.stats[_ngcontent-%COMP%]{display:flex;justify-content:space-around;margin:20px 0;padding:15px;background:#e9ecef;border-radius:8px}.stats[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-weight:700}.users-list[_ngcontent-%COMP%]{display:grid;gap:15px;margin:20px 0}.user-card[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:15px;border:1px solid #ddd;border-radius:8px;background:#fff;transition:all .3s ease}.user-card[_ngcontent-%COMP%]:hover{box-shadow:0 4px 8px #0000001a}.user-card.inactive[_ngcontent-%COMP%]{opacity:.6;background:#f8f9fa}.user-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 5px;color:#333}.user-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 10px;color:#666;font-size:14px}.role-badge[_ngcontent-%COMP%], .status-badge[_ngcontent-%COMP%]{display:inline-block;padding:4px 8px;border-radius:4px;font-size:12px;font-weight:700;margin-right:8px}.role-admin[_ngcontent-%COMP%]{background:#dc3545;color:#fff}.role-manager[_ngcontent-%COMP%]{background:#ffc107;color:#000}.role-user[_ngcontent-%COMP%]{background:#28a745;color:#fff}.status-badge[_ngcontent-%COMP%]{background:#6c757d;color:#fff}.status-badge.active[_ngcontent-%COMP%]{background:#28a745}.btn-sm[_ngcontent-%COMP%]{padding:6px 12px;font-size:12px}.info-box[_ngcontent-%COMP%]{background:#f8f9fa;padding:20px;border-radius:8px;border-left:4px solid #17a2b8;margin-top:30px}.info-box[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin-top:0;color:#17a2b8}.info-box[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{margin:10px 0;padding-left:20px}h2[_ngcontent-%COMP%]{color:#1976d2;margin-bottom:20px}@media (max-width: 768px){.controls[_ngcontent-%COMP%]{grid-template-columns:1fr}.stats[_ngcontent-%COMP%]{flex-direction:column;gap:10px}.user-card[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:10px}}"]})}};export{Xe as UsersComponent};
