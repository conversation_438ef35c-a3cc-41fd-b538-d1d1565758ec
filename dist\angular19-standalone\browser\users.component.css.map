{"version": 3, "sources": ["angular:styles/component:css;825fe552dc4845aab63c0211b89beebe3250a7e377847ecaf9a277ea7522653c;D:/learning/angular/standalone/src/app/components/users/users.component.ts"], "sourcesContent": ["\n    .users {\n      max-width: 900px;\n      margin: 0 auto;\n    }\n\n    .controls {\n      display: grid;\n      grid-template-columns: 1fr 1fr 1fr;\n      gap: 20px;\n      margin: 20px 0;\n      padding: 20px;\n      background: #f8f9fa;\n      border-radius: 8px;\n    }\n\n    .search-input, .filter-select {\n      width: 100%;\n      padding: 8px;\n      border: 1px solid #ddd;\n      border-radius: 4px;\n      font-size: 14px;\n    }\n\n    .stats {\n      display: flex;\n      justify-content: space-around;\n      margin: 20px 0;\n      padding: 15px;\n      background: #e9ecef;\n      border-radius: 8px;\n    }\n\n    .stats p {\n      margin: 0;\n      font-weight: bold;\n    }\n\n    .users-list {\n      display: grid;\n      gap: 15px;\n      margin: 20px 0;\n    }\n\n    .user-card {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 15px;\n      border: 1px solid #ddd;\n      border-radius: 8px;\n      background: white;\n      transition: all 0.3s ease;\n    }\n\n    .user-card:hover {\n      box-shadow: 0 4px 8px rgba(0,0,0,0.1);\n    }\n\n    .user-card.inactive {\n      opacity: 0.6;\n      background: #f8f9fa;\n    }\n\n    .user-info h4 {\n      margin: 0 0 5px 0;\n      color: #333;\n    }\n\n    .user-info p {\n      margin: 0 0 10px 0;\n      color: #666;\n      font-size: 14px;\n    }\n\n    .role-badge, .status-badge {\n      display: inline-block;\n      padding: 4px 8px;\n      border-radius: 4px;\n      font-size: 12px;\n      font-weight: bold;\n      margin-right: 8px;\n    }\n\n    .role-admin { background: #dc3545; color: white; }\n    .role-manager { background: #ffc107; color: black; }\n    .role-user { background: #28a745; color: white; }\n\n    .status-badge {\n      background: #6c757d;\n      color: white;\n    }\n\n    .status-badge.active {\n      background: #28a745;\n    }\n\n    .btn-sm {\n      padding: 6px 12px;\n      font-size: 12px;\n    }\n\n    .info-box {\n      background: #f8f9fa;\n      padding: 20px;\n      border-radius: 8px;\n      border-left: 4px solid #17a2b8;\n      margin-top: 30px;\n    }\n\n    .info-box h4 {\n      margin-top: 0;\n      color: #17a2b8;\n    }\n\n    .info-box ul {\n      margin: 10px 0;\n      padding-left: 20px;\n    }\n\n    h2 {\n      color: #1976d2;\n      margin-bottom: 20px;\n    }\n\n    @media (max-width: 768px) {\n      .controls {\n        grid-template-columns: 1fr;\n      }\n      \n      .stats {\n        flex-direction: column;\n        gap: 10px;\n      }\n      \n      .user-card {\n        flex-direction: column;\n        align-items: flex-start;\n        gap: 10px;\n      }\n    }\n  "], "mappings": ";AACI,CAAC;AACC,aAAW;AACX,UAAQ,EAAE;AACZ;AAEA,CAAC;AACC,WAAS;AACT,yBAAuB,IAAI,IAAI;AAC/B,OAAK;AACL,UAAQ,KAAK;AACb,WAAS;AACT,cAAY;AACZ,iBAAe;AACjB;AAEA,CAAC;AAAc,CAAC;AACd,SAAO;AACP,WAAS;AACT,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,aAAW;AACb;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,UAAQ,KAAK;AACb,WAAS;AACT,cAAY;AACZ,iBAAe;AACjB;AAEA,CATC,MASM;AACL,UAAQ;AACR,eAAa;AACf;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACL,UAAQ,KAAK;AACf;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,WAAS;AACT,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,cAAY;AACZ,cAAY,IAAI,KAAK;AACvB;AAEA,CAXC,SAWS;AACR,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;AACnC;AAEA,CAfC,SAeS,CAAC;AACT,WAAS;AACT,cAAY;AACd;AAEA,CAAC,UAAU;AACT,UAAQ,EAAE,EAAE,IAAI;AAChB,SAAO;AACT;AAEA,CALC,UAKU;AACT,UAAQ,EAAE,EAAE,KAAK;AACjB,SAAO;AACP,aAAW;AACb;AAEA,CAAC;AAAY,CAAC;AACZ,WAAS;AACT,WAAS,IAAI;AACb,iBAAe;AACf,aAAW;AACX,eAAa;AACb,gBAAc;AAChB;AAEA,CAAC;AAAa,cAAY;AAAS,SAAO;AAAO;AACjD,CAAC;AAAe,cAAY;AAAS,SAAO;AAAO;AACnD,CAAC;AAAY,cAAY;AAAS,SAAO;AAAO;AAEhD,CAbc;AAcZ,cAAY;AACZ,SAAO;AACT;AAEA,CAlBc,YAkBD,CAAC;AACZ,cAAY;AACd;AAEA,CAAC;AACC,WAAS,IAAI;AACb,aAAW;AACb;AAEA,CAAC;AACC,cAAY;AACZ,WAAS;AACT,iBAAe;AACf,eAAa,IAAI,MAAM;AACvB,cAAY;AACd;AAEA,CARC,SAQS;AACR,cAAY;AACZ,SAAO;AACT;AAEA,CAbC,SAaS;AACR,UAAQ,KAAK;AACb,gBAAc;AAChB;AAEA;AACE,SAAO;AACP,iBAAe;AACjB;AAEA,OAAO,CAAC,SAAS,EAAE;AACjB,GAxHD;AAyHG,2BAAuB;AACzB;AAEA,GA1GD;AA2GG,oBAAgB;AAChB,SAAK;AACP;AAEA,GA3FD;AA4FG,oBAAgB;AAChB,iBAAa;AACb,SAAK;AACP;AACF;", "names": []}