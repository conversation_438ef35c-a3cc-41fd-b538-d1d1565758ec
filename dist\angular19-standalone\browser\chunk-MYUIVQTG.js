import{La as e,Ma as t,Pa as a,Ua as n,Va as g,Wa as p,fb as m,ha as s,nb as x,qa as l,ya as d}from"./chunk-XF3IHJ6T.js";var f=class u{constructor(){this.count=s(0);this.step=s(1);this.doubleCount=m(()=>this.count()*2);this.countStatus=m(()=>{let i=this.count();return i===0?"Zero":i>0?"Positive":"Negative"})}increment(){this.count.update(i=>i+this.step())}decrement(){this.count.update(i=>i-this.step())}reset(){this.count.set(0)}setStep(i){let r=i.target;this.step.set(parseInt(r.value))}static{this.\u0275fac=function(r){return new(r||u)}}static{this.\u0275cmp=d({type:u,selectors:[["app-counter"]],decls:38,vars:3,consts:[[1,"counter"],[1,"card"],[1,"counter-display"],[1,"count-value"],[1,"count-info"],[1,"counter-controls"],[1,"btn",3,"click"],[1,"btn","btn-secondary",3,"click"],[1,"step-controls"],["for","step"],["id","step",1,"step-select",3,"change"],["value","1"],["value","2"],["value","5"],["value","10"],[1,"info-box"]],template:function(r,o){r&1&&(e(0,"div",0)(1,"div",1)(2,"h2"),n(3,"Counter Example with Signals"),t(),e(4,"p"),n(5,"This demonstrates Angular's new signal-based reactivity system."),t(),e(6,"div",2)(7,"div",3),n(8),t(),e(9,"div",4)(10,"p"),n(11),t(),e(12,"p"),n(13),t()()(),e(14,"div",5)(15,"button",6),a("click",function(){return o.increment()}),n(16," + Increment "),t(),e(17,"button",6),a("click",function(){return o.decrement()}),n(18," - Decrement "),t(),e(19,"button",7),a("click",function(){return o.reset()}),n(20," Reset "),t()(),e(21,"div",8)(22,"label",9),n(23,"Step Size:"),t(),e(24,"select",10),a("change",function(C){return o.setStep(C)}),e(25,"option",11),n(26,"1"),t(),e(27,"option",12),n(28,"2"),t(),e(29,"option",13),n(30,"5"),t(),e(31,"option",14),n(32,"10"),t()()(),e(33,"div",15)(34,"h4"),n(35,"About Signals"),t(),e(36,"p"),n(37," Signals provide a new way to manage reactive state in Angular. They automatically track dependencies and update the UI when values change, providing better performance and developer experience. "),t()()()()),r&2&&(l(8),g(o.count()),l(3),p("Double: ",o.doubleCount(),""),l(2),p("Status: ",o.countStatus(),""))},dependencies:[x],styles:[".counter[_ngcontent-%COMP%]{max-width:600px;margin:0 auto}.counter-display[_ngcontent-%COMP%]{text-align:center;margin:30px 0}.count-value[_ngcontent-%COMP%]{font-size:4rem;font-weight:700;color:#1976d2;margin-bottom:20px}.count-info[_ngcontent-%COMP%]{display:flex;justify-content:space-around;margin:20px 0}.count-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.2rem;margin:0}.counter-controls[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:10px;margin:30px 0}.step-controls[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:10px;margin:20px 0}.step-select[_ngcontent-%COMP%]{padding:8px;border:1px solid #ddd;border-radius:4px;font-size:14px}.info-box[_ngcontent-%COMP%]{background:#f8f9fa;padding:20px;border-radius:8px;border-left:4px solid #28a745;margin-top:30px}.info-box[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin-top:0;color:#28a745}h2[_ngcontent-%COMP%]{color:#1976d2;text-align:center;margin-bottom:20px}"]})}};export{f as CounterComponent};
