{"version": 3, "sources": ["src/app/components/about/about.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\n@Component({\n  selector: 'app-about',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <div class=\"about\">\n      <div class=\"card\">\n        <h2>About This Application</h2>\n        <p>This is a demonstration of Angular 19's standalone components feature.</p>\n\n        <div class=\"tech-stack\">\n          <h3>Technology Stack</h3>\n          <div class=\"tech-grid\">\n            <div class=\"tech-item\">\n              <h4>Angular 19</h4>\n              <p>Latest version with standalone components</p>\n            </div>\n            <div class=\"tech-item\">\n              <h4>TypeScript</h4>\n              <p>Type-safe JavaScript development</p>\n            </div>\n            <div class=\"tech-item\">\n              <h4>Signals</h4>\n              <p>New reactive state management</p>\n            </div>\n            <div class=\"tech-item\">\n              <h4>Standalone Components</h4>\n              <p>No NgModules required</p>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"features-section\">\n          <h3>Features Demonstrated</h3>\n          <div class=\"features-grid\">\n            <div class=\"feature-card\">\n              <h4>🚀 Standalone Components</h4>\n              <p>Components that can import their own dependencies without NgModules</p>\n            </div>\n            <div class=\"feature-card\">\n              <h4>📡 Signals</h4>\n              <p>New reactive state management system for better performance</p>\n            </div>\n            <div class=\"feature-card\">\n              <h4>🔄 Lazy Loading</h4>\n              <p>Route-based code splitting with standalone components</p>\n            </div>\n            <div class=\"feature-card\">\n              <h4>🎯 Modern Routing</h4>\n              <p>Simplified routing configuration with functional guards</p>\n            </div>\n            <div class=\"feature-card\">\n              <h4>📱 Responsive Design</h4>\n              <p>Mobile-first CSS with modern layout techniques</p>\n            </div>\n            <div class=\"feature-card\">\n              <h4>⚡ Performance</h4>\n              <p>Optimized bundle size and runtime performance</p>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"architecture-section\">\n          <h3>Application Architecture</h3>\n          <div class=\"architecture-info\">\n            <div class=\"arch-item\">\n              <h4>Component Structure</h4>\n              <ul>\n                <li>App Component (Root)</li>\n                <li>Home Component (Landing page)</li>\n                <li>Counter Component (Signals demo)</li>\n                <li>Users Component (List management)</li>\n                <li>About Component (This page)</li>\n              </ul>\n            </div>\n            <div class=\"arch-item\">\n              <h4>Key Benefits</h4>\n              <ul>\n                <li>Simplified architecture</li>\n                <li>Better tree-shaking</li>\n                <li>Reduced boilerplate</li>\n                <li>Improved developer experience</li>\n                <li>Better performance</li>\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"version-info\">\n          <h3>Version Information</h3>\n          <div class=\"version-grid\">\n            <div class=\"version-item\">\n              <strong>Angular:</strong> 19.x\n            </div>\n            <div class=\"version-item\">\n              <strong>TypeScript:</strong> 5.6+\n            </div>\n            <div class=\"version-item\">\n              <strong>Node.js:</strong> 18+\n            </div>\n            <div class=\"version-item\">\n              <strong>Build:</strong> {{ buildDate }}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .about {\n      max-width: 1000px;\n      margin: 0 auto;\n    }\n\n    .tech-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 20px;\n      margin: 20px 0;\n    }\n\n    .tech-item {\n      padding: 20px;\n      background: #f8f9fa;\n      border-radius: 8px;\n      text-align: center;\n      border: 1px solid #e9ecef;\n    }\n\n    .tech-item h4 {\n      margin: 0 0 10px 0;\n      color: #1976d2;\n    }\n\n    .tech-item p {\n      margin: 0;\n      color: #666;\n      font-size: 14px;\n    }\n\n    .features-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n      gap: 20px;\n      margin: 20px 0;\n    }\n\n    .feature-card {\n      padding: 20px;\n      background: white;\n      border: 1px solid #e9ecef;\n      border-radius: 8px;\n      box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n      transition: transform 0.3s ease;\n    }\n\n    .feature-card:hover {\n      transform: translateY(-2px);\n      box-shadow: 0 4px 8px rgba(0,0,0,0.15);\n    }\n\n    .feature-card h4 {\n      margin: 0 0 10px 0;\n      color: #333;\n      font-size: 16px;\n    }\n\n    .feature-card p {\n      margin: 0;\n      color: #666;\n      font-size: 14px;\n      line-height: 1.5;\n    }\n\n    .architecture-info {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: 30px;\n      margin: 20px 0;\n    }\n\n    .arch-item {\n      background: #f8f9fa;\n      padding: 20px;\n      border-radius: 8px;\n    }\n\n    .arch-item h4 {\n      margin: 0 0 15px 0;\n      color: #1976d2;\n    }\n\n    .arch-item ul {\n      margin: 0;\n      padding-left: 20px;\n    }\n\n    .arch-item li {\n      margin: 8px 0;\n      color: #555;\n    }\n\n    .version-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 15px;\n      margin: 20px 0;\n    }\n\n    .version-item {\n      padding: 15px;\n      background: #e9ecef;\n      border-radius: 8px;\n      text-align: center;\n      font-size: 14px;\n    }\n\n    h2 {\n      color: #1976d2;\n      margin-bottom: 20px;\n    }\n\n    h3 {\n      color: #333;\n      margin: 30px 0 15px 0;\n      border-bottom: 2px solid #1976d2;\n      padding-bottom: 5px;\n    }\n\n    .features-section,\n    .architecture-section,\n    .version-info {\n      margin: 40px 0;\n    }\n\n    @media (max-width: 768px) {\n      .architecture-info {\n        grid-template-columns: 1fr;\n      }\n      \n      .features-grid {\n        grid-template-columns: 1fr;\n      }\n      \n      .tech-grid {\n        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n      }\n    }\n  `]\n})\nexport class AboutComponent {\n  buildDate = new Date().toLocaleDateString();\n}\n"], "mappings": ";;;;;;;;;;;;;;AA6PM,IAAO,iBAAP,MAAO,gBAAc;EA1P3B,cAAA;AA2PE,SAAA,aAAY,oBAAI,KAAI,GAAG,mBAAkB;;;;uCAD9B,iBAAc;IAAA;EAAA;;yEAAd,iBAAc,WAAA,CAAA,CAAA,WAAA,CAAA,GAAA,OAAA,KAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,OAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,cAAA,CAAA,GAAA,UAAA,SAAA,wBAAA,IAAA,KAAA;AAAA,UAAA,KAAA,GAAA;AArPvB,QAAA,yBAAA,GAAA,OAAA,CAAA,EAAmB,GAAA,OAAA,CAAA,EACC,GAAA,IAAA;AACZ,QAAA,iBAAA,GAAA,wBAAA;AAAsB,QAAA,uBAAA;AAC1B,QAAA,yBAAA,GAAA,GAAA;AAAG,QAAA,iBAAA,GAAA,wEAAA;AAAsE,QAAA,uBAAA;AAEzE,QAAA,yBAAA,GAAA,OAAA,CAAA,EAAwB,GAAA,IAAA;AAClB,QAAA,iBAAA,GAAA,kBAAA;AAAgB,QAAA,uBAAA;AACpB,QAAA,yBAAA,GAAA,OAAA,CAAA,EAAuB,IAAA,OAAA,CAAA,EACE,IAAA,IAAA;AACjB,QAAA,iBAAA,IAAA,YAAA;AAAU,QAAA,uBAAA;AACd,QAAA,yBAAA,IAAA,GAAA;AAAG,QAAA,iBAAA,IAAA,2CAAA;AAAyC,QAAA,uBAAA,EAAI;AAElD,QAAA,yBAAA,IAAA,OAAA,CAAA,EAAuB,IAAA,IAAA;AACjB,QAAA,iBAAA,IAAA,YAAA;AAAU,QAAA,uBAAA;AACd,QAAA,yBAAA,IAAA,GAAA;AAAG,QAAA,iBAAA,IAAA,kCAAA;AAAgC,QAAA,uBAAA,EAAI;AAEzC,QAAA,yBAAA,IAAA,OAAA,CAAA,EAAuB,IAAA,IAAA;AACjB,QAAA,iBAAA,IAAA,SAAA;AAAO,QAAA,uBAAA;AACX,QAAA,yBAAA,IAAA,GAAA;AAAG,QAAA,iBAAA,IAAA,+BAAA;AAA6B,QAAA,uBAAA,EAAI;AAEtC,QAAA,yBAAA,IAAA,OAAA,CAAA,EAAuB,IAAA,IAAA;AACjB,QAAA,iBAAA,IAAA,uBAAA;AAAqB,QAAA,uBAAA;AACzB,QAAA,yBAAA,IAAA,GAAA;AAAG,QAAA,iBAAA,IAAA,uBAAA;AAAqB,QAAA,uBAAA,EAAI,EACxB,EACF;AAGR,QAAA,yBAAA,IAAA,OAAA,CAAA,EAA8B,IAAA,IAAA;AACxB,QAAA,iBAAA,IAAA,uBAAA;AAAqB,QAAA,uBAAA;AACzB,QAAA,yBAAA,IAAA,OAAA,CAAA,EAA2B,IAAA,OAAA,CAAA,EACC,IAAA,IAAA;AACpB,QAAA,iBAAA,IAAA,iCAAA;AAAwB,QAAA,uBAAA;AAC5B,QAAA,yBAAA,IAAA,GAAA;AAAG,QAAA,iBAAA,IAAA,qEAAA;AAAmE,QAAA,uBAAA,EAAI;AAE5E,QAAA,yBAAA,IAAA,OAAA,CAAA,EAA0B,IAAA,IAAA;AACpB,QAAA,iBAAA,IAAA,mBAAA;AAAU,QAAA,uBAAA;AACd,QAAA,yBAAA,IAAA,GAAA;AAAG,QAAA,iBAAA,IAAA,6DAAA;AAA2D,QAAA,uBAAA,EAAI;AAEpE,QAAA,yBAAA,IAAA,OAAA,CAAA,EAA0B,IAAA,IAAA;AACpB,QAAA,iBAAA,IAAA,wBAAA;AAAe,QAAA,uBAAA;AACnB,QAAA,yBAAA,IAAA,GAAA;AAAG,QAAA,iBAAA,IAAA,uDAAA;AAAqD,QAAA,uBAAA,EAAI;AAE9D,QAAA,yBAAA,IAAA,OAAA,CAAA,EAA0B,IAAA,IAAA;AACpB,QAAA,iBAAA,IAAA,0BAAA;AAAiB,QAAA,uBAAA;AACrB,QAAA,yBAAA,IAAA,GAAA;AAAG,QAAA,iBAAA,IAAA,yDAAA;AAAuD,QAAA,uBAAA,EAAI;AAEhE,QAAA,yBAAA,IAAA,OAAA,CAAA,EAA0B,IAAA,IAAA;AACpB,QAAA,iBAAA,IAAA,6BAAA;AAAoB,QAAA,uBAAA;AACxB,QAAA,yBAAA,IAAA,GAAA;AAAG,QAAA,iBAAA,IAAA,gDAAA;AAA8C,QAAA,uBAAA,EAAI;AAEvD,QAAA,yBAAA,IAAA,OAAA,CAAA,EAA0B,IAAA,IAAA;AACpB,QAAA,iBAAA,IAAA,oBAAA;AAAa,QAAA,uBAAA;AACjB,QAAA,yBAAA,IAAA,GAAA;AAAG,QAAA,iBAAA,IAAA,+CAAA;AAA6C,QAAA,uBAAA,EAAI,EAChD,EACF;AAGR,QAAA,yBAAA,IAAA,OAAA,CAAA,EAAkC,IAAA,IAAA;AAC5B,QAAA,iBAAA,IAAA,0BAAA;AAAwB,QAAA,uBAAA;AAC5B,QAAA,yBAAA,IAAA,OAAA,CAAA,EAA+B,IAAA,OAAA,EAAA,EACN,IAAA,IAAA;AACjB,QAAA,iBAAA,IAAA,qBAAA;AAAmB,QAAA,uBAAA;AACvB,QAAA,yBAAA,IAAA,IAAA,EAAI,IAAA,IAAA;AACE,QAAA,iBAAA,IAAA,sBAAA;AAAoB,QAAA,uBAAA;AACxB,QAAA,yBAAA,IAAA,IAAA;AAAI,QAAA,iBAAA,IAAA,+BAAA;AAA6B,QAAA,uBAAA;AACjC,QAAA,yBAAA,IAAA,IAAA;AAAI,QAAA,iBAAA,IAAA,kCAAA;AAAgC,QAAA,uBAAA;AACpC,QAAA,yBAAA,IAAA,IAAA;AAAI,QAAA,iBAAA,IAAA,mCAAA;AAAiC,QAAA,uBAAA;AACrC,QAAA,yBAAA,IAAA,IAAA;AAAI,QAAA,iBAAA,IAAA,6BAAA;AAA2B,QAAA,uBAAA,EAAK,EACjC;AAEP,QAAA,yBAAA,IAAA,OAAA,EAAA,EAAuB,IAAA,IAAA;AACjB,QAAA,iBAAA,IAAA,cAAA;AAAY,QAAA,uBAAA;AAChB,QAAA,yBAAA,IAAA,IAAA,EAAI,IAAA,IAAA;AACE,QAAA,iBAAA,IAAA,yBAAA;AAAuB,QAAA,uBAAA;AAC3B,QAAA,yBAAA,IAAA,IAAA;AAAI,QAAA,iBAAA,IAAA,qBAAA;AAAmB,QAAA,uBAAA;AACvB,QAAA,yBAAA,IAAA,IAAA;AAAI,QAAA,iBAAA,IAAA,qBAAA;AAAmB,QAAA,uBAAA;AACvB,QAAA,yBAAA,IAAA,IAAA;AAAI,QAAA,iBAAA,IAAA,+BAAA;AAA6B,QAAA,uBAAA;AACjC,QAAA,yBAAA,IAAA,IAAA;AAAI,QAAA,iBAAA,IAAA,oBAAA;AAAkB,QAAA,uBAAA,EAAK,EACxB,EACD,EACF;AAGR,QAAA,yBAAA,IAAA,OAAA,EAAA,EAA0B,IAAA,IAAA;AACpB,QAAA,iBAAA,IAAA,qBAAA;AAAmB,QAAA,uBAAA;AACvB,QAAA,yBAAA,IAAA,OAAA,EAAA,EAA0B,KAAA,OAAA,EAAA,EACE,KAAA,QAAA;AAChB,QAAA,iBAAA,KAAA,UAAA;AAAQ,QAAA,uBAAA;AAAU,QAAA,iBAAA,KAAA,QAAA;AAC5B,QAAA,uBAAA;AACA,QAAA,yBAAA,KAAA,OAAA,EAAA,EAA0B,KAAA,QAAA;AAChB,QAAA,iBAAA,KAAA,aAAA;AAAW,QAAA,uBAAA;AAAU,QAAA,iBAAA,KAAA,QAAA;AAC/B,QAAA,uBAAA;AACA,QAAA,yBAAA,KAAA,OAAA,EAAA,EAA0B,KAAA,QAAA;AAChB,QAAA,iBAAA,KAAA,UAAA;AAAQ,QAAA,uBAAA;AAAU,QAAA,iBAAA,KAAA,OAAA;AAC5B,QAAA,uBAAA;AACA,QAAA,yBAAA,KAAA,OAAA,EAAA,EAA0B,KAAA,QAAA;AAChB,QAAA,iBAAA,KAAA,QAAA;AAAM,QAAA,uBAAA;AAAU,QAAA,iBAAA,GAAA;AAC1B,QAAA,uBAAA,EAAM,EACF,EACF,EACF;;;AAJ0B,QAAA,oBAAA,GAAA;AAAA,QAAA,6BAAA,KAAA,IAAA,WAAA,GAAA;;sBAlG1B,YAAY,GAAA,QAAA,CAAA,qyFAAA,EAAA,CAAA;EAAA;;;sEAuPX,gBAAc,CAAA;UA1P1B;uBACW,aAAW,YACT,MAAI,SACP,CAAC,YAAY,GAAC,UACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAuGT,QAAA,CAAA,m2EAAA,EAAA,CAAA;;;;6EA+IU,gBAAc,EAAA,WAAA,kBAAA,UAAA,+CAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}